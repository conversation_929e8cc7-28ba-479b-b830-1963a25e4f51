2025-07-24 15:18:11,158 - neo4j_connection - INFO - 初始化 Neo4j 连接: bolt://localhost:7687, 用户: neo4j
2025-07-24 15:18:11,266 - neo4j_connection - INFO - Neo4j 连接验证成功
2025-07-24 15:18:11,266 - __main__ - INFO - 成功连接到Neo4j数据库
2025-07-24 15:18:11,266 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 15:18:11,266 - __main__ - INFO - 开始改进异步导入实验，批次大小: [10, 50, 100, 150, 200, 250]
2025-07-24 15:18:11,268 - __main__ - INFO - 读取到 260 个节点
2025-07-24 15:18:11,269 - __main__ - INFO - 去重后节点数: 258
2025-07-24 15:18:11,269 - __main__ - INFO - 
==================================================
2025-07-24 15:18:11,269 - __main__ - INFO - 开始测试批次大小: 10
2025-07-24 15:18:11,269 - __main__ - INFO - ==================================================
2025-07-24 15:18:11,376 - neo4j_connection - INFO - 清空所有节点和关系
2025-07-24 15:18:11,376 - __main__ - INFO - 数据库已清空
2025-07-24 15:18:11,377 - __main__ - ERROR - 批次 10 执行失败: asyncio.run() cannot be called from a running event loop
2025-07-24 15:18:11,382 - __main__ - INFO - 
==================================================
2025-07-24 15:18:11,383 - __main__ - INFO - 开始测试批次大小: 50
2025-07-24 15:18:11,383 - __main__ - INFO - ==================================================
2025-07-24 15:18:11,385 - neo4j_connection - INFO - 清空所有节点和关系
2025-07-24 15:18:11,385 - __main__ - INFO - 数据库已清空
2025-07-24 15:18:11,385 - __main__ - ERROR - 批次 50 执行失败: asyncio.run() cannot be called from a running event loop
2025-07-24 15:18:11,385 - __main__ - INFO - 
==================================================
2025-07-24 15:18:11,387 - __main__ - INFO - 开始测试批次大小: 100
2025-07-24 15:18:11,387 - __main__ - INFO - ==================================================
2025-07-24 15:18:11,389 - neo4j_connection - INFO - 清空所有节点和关系
2025-07-24 15:18:11,389 - __main__ - INFO - 数据库已清空
2025-07-24 15:18:11,389 - __main__ - ERROR - 批次 100 执行失败: asyncio.run() cannot be called from a running event loop
2025-07-24 15:18:11,390 - __main__ - INFO - 
==================================================
2025-07-24 15:18:11,390 - __main__ - INFO - 开始测试批次大小: 150
2025-07-24 15:18:11,390 - __main__ - INFO - ==================================================
2025-07-24 15:18:11,392 - neo4j_connection - INFO - 清空所有节点和关系
2025-07-24 15:18:11,393 - __main__ - INFO - 数据库已清空
2025-07-24 15:18:11,393 - __main__ - ERROR - 批次 150 执行失败: asyncio.run() cannot be called from a running event loop
2025-07-24 15:18:11,393 - __main__ - INFO - 
==================================================
2025-07-24 15:18:11,393 - __main__ - INFO - 开始测试批次大小: 200
2025-07-24 15:18:11,393 - __main__ - INFO - ==================================================
2025-07-24 15:18:11,395 - neo4j_connection - INFO - 清空所有节点和关系
2025-07-24 15:18:11,395 - __main__ - INFO - 数据库已清空
2025-07-24 15:18:11,395 - __main__ - ERROR - 批次 200 执行失败: asyncio.run() cannot be called from a running event loop
2025-07-24 15:18:11,396 - __main__ - INFO - 
==================================================
2025-07-24 15:18:11,396 - __main__ - INFO - 开始测试批次大小: 250
2025-07-24 15:18:11,396 - __main__ - INFO - ==================================================
2025-07-24 15:18:11,399 - neo4j_connection - INFO - 清空所有节点和关系
2025-07-24 15:18:11,400 - __main__ - INFO - 数据库已清空
2025-07-24 15:18:11,400 - __main__ - ERROR - 批次 250 执行失败: asyncio.run() cannot be called from a running event loop
2025-07-24 15:18:11,401 - __main__ - INFO - 实验结果已保存到: improved_async_results_20250724_151811.json
2025-07-24 15:18:11,401 - __main__ - INFO - 
============================================================
2025-07-24 15:18:11,402 - __main__ - INFO - 改进异步导入实验汇总报告
2025-07-24 15:18:11,402 - __main__ - INFO - ============================================================
2025-07-24 15:18:11,402 - __main__ - INFO - ============================================================
2025-07-24 15:18:11,402 - __main__ - INFO - 改进异步导入实验完成！
2025-07-24 15:18:11,403 - neo4j_connection - INFO - Neo4j 连接已关闭
2025-07-24 15:20:33,167 - neo4j_connection - INFO - 初始化 Neo4j 连接: bolt://localhost:7687, 用户: neo4j
2025-07-24 15:20:33,206 - neo4j_connection - INFO - Neo4j 连接验证成功
2025-07-24 15:20:33,206 - __main__ - INFO - 成功连接到Neo4j数据库
2025-07-24 15:20:33,206 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 15:20:33,207 - __main__ - INFO - 开始改进异步导入实验，批次大小: [10, 50, 100, 150, 200, 250]
2025-07-24 15:20:33,209 - __main__ - INFO - 读取到 260 个节点
2025-07-24 15:20:33,210 - __main__ - INFO - 去重后节点数: 258
2025-07-24 15:20:33,210 - __main__ - INFO - 
==================================================
2025-07-24 15:20:33,210 - __main__ - INFO - 开始测试批次大小: 10
2025-07-24 15:20:33,210 - __main__ - INFO - ==================================================
2025-07-24 15:20:33,219 - neo4j_connection - INFO - 清空所有节点和关系
2025-07-24 15:20:33,219 - __main__ - INFO - 数据库已清空
2025-07-24 15:20:33,219 - __main__ - INFO - 开始改进异步导入: improved_async_batch_10_nodes, 节点数: 10
2025-07-24 15:20:33,220 - __main__ - INFO - 分成 1 个子批次，每批次最多 15 个节点
2025-07-24 15:20:33,220 - __main__ - INFO - 开始处理批次 1/1
2025-07-24 15:20:33,974 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 15:20:33,974 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 15:20:49,502 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 15:20:49,607 - text_processor - INFO - 成功处理实体: 布达拉宫
2025-07-24 15:20:57,516 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 15:20:57,535 - text_processor - INFO - 成功处理实体: 大昭寺
2025-07-24 15:21:05,990 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 15:21:05,991 - text_processor - INFO - 调整API并发数为: 4
2025-07-24 15:21:06,061 - text_processor - INFO - 成功处理实体: 纳木措
2025-07-24 15:21:10,052 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 15:21:10,064 - text_processor - INFO - 成功处理实体: 色拉寺
2025-07-24 15:21:25,799 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 15:21:25,823 - text_processor - INFO - 成功处理实体: 八廓街
2025-07-24 15:21:32,379 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 15:21:38,743 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 15:21:38,745 - text_processor - INFO - 调整API并发数为: 5
2025-07-24 15:21:38,799 - text_processor - INFO - 成功处理实体: 《文成公主》大型史诗剧
2025-07-24 15:21:50,201 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 15:21:50,221 - text_processor - INFO - 成功处理实体: 罗布林卡
2025-07-24 15:22:01,785 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 15:22:01,799 - text_processor - INFO - 成功处理实体: 药王山
2025-07-24 15:22:07,630 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 15:22:07,666 - text_processor - INFO - 成功处理实体: 宗角禄康公园
2025-07-24 15:22:14,159 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 15:22:14,161 - text_processor - INFO - 调整API并发数为: 6
2025-07-24 15:22:14,207 - text_processor - INFO - 成功处理实体: 哲蚌寺
2025-07-24 15:22:14,207 - text_processor - INFO - 过滤后景点数量: 10
2025-07-24 15:22:21,101 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 15:22:23,323 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 15:22:25,853 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 15:22:26,091 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 15:22:28,594 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 15:22:28,659 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 15:22:30,396 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 15:22:36,887 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 15:22:38,081 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 15:22:41,335 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 15:22:49,096 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 15:22:49,098 - text_processor - INFO - 调整API并发数为: 7
2025-07-24 15:22:49,479 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 15:22:53,090 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 15:22:54,080 - text_processor - ERROR - LLM 调用失败: 
2025-07-24 15:22:55,656 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 15:22:56,124 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 15:23:01,706 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 15:23:05,781 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
