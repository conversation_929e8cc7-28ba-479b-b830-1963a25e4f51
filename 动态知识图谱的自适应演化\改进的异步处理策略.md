# 改进异步处理以避免API限流的策略

## 🎯 核心问题
异步处理因为API限流反而比同步处理慢，需要在保留异步优势的同时避免限流问题。

## 🔧 改进策略

### 1. 智能并发控制

**问题**: 固定的10个并发容易触发API限流
**解决方案**: 动态调整并发数

```python
class AdaptiveSemaphore:
    def __init__(self, initial_permits=3, min_permits=1, max_permits=6):
        self.semaphore = asyncio.Semaphore(initial_permits)
        self.current_permits = initial_permits
        self.min_permits = min_permits
        self.max_permits = max_permits
        self.success_count = 0
        self.error_count = 0
        self.last_adjustment = time.time()
        
    async def __aenter__(self):
        await self.semaphore.acquire()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        self.semaphore.release()
        
        # 根据成功/失败情况动态调整
        current_time = time.time()
        if current_time - self.last_adjustment > 30:  # 每30秒评估一次
            if exc_type is None:
                self.success_count += 1
            else:
                self.error_count += 1
                
            # 调整逻辑
            if self.error_count > self.success_count:
                # 错误率高，减少并发
                self.current_permits = max(self.min_permits, self.current_permits - 1)
                self._update_semaphore()
                logger.info(f"检测到错误率高，降低并发至: {self.current_permits}")
            elif self.success_count > self.error_count * 3:
                # 成功率很高，适度增加并发
                self.current_permits = min(self.max_permits, self.current_permits + 1)
                self._update_semaphore()
                logger.info(f"成功率高，提升并发至: {self.current_permits}")
                
            self.last_adjustment = current_time
            self.success_count = 0
            self.error_count = 0
    
    def _update_semaphore(self):
        self.semaphore = asyncio.Semaphore(self.current_permits)

# 使用自适应信号量
request_semaphore = AdaptiveSemaphore(initial_permits=3)
```

### 2. 请求间隔控制

**问题**: 并发请求同时到达，触发突发限流
**解决方案**: 添加请求间隔

```python
async def call_deepseek_with_retry(prompt: str, model_name: str = "default") -> str:
    # ... 其他代码 ...
    
    async with request_semaphore:
        # 关键改进：添加请求间隔
        await asyncio.sleep(0.5)  # 每个请求间隔0.5秒
        
        async with AsyncClient(timeout=config["timeout"]) as client:
            # ... API调用代码 ...
```

### 3. 改进批处理策略

**问题**: 子批次串行处理，没有发挥异步优势
**解决方案**: 真正的并发批处理

```python
async def import_batch_async_improved(self, nodes: List[Dict], batch_name: str, batch_size: int = 15) -> Dict:
    """改进的异步导入 - 真正的并发处理"""
    logger.info(f"开始改进异步导入: {batch_name}, 节点数: {len(nodes)}")
    start_time = time.time()
    
    try:
        # 将节点分成更大的批次
        batches = [nodes[i:i + batch_size] for i in range(0, len(nodes), batch_size)]
        logger.info(f"分成 {len(batches)} 个子批次，每批次最多 {batch_size} 个节点")
        
        # 限制批次级别的并发
        batch_semaphore = asyncio.Semaphore(2)  # 最多2个批次并发
        
        async def process_batch_with_control(batch, batch_idx):
            async with batch_semaphore:
                logger.info(f"开始处理批次 {batch_idx + 1}/{len(batches)}")
                # 批次间添加随机延迟，避免同时开始
                await asyncio.sleep(random.uniform(0.1, 0.5))
                
                return await process_json_chunk(
                    neo4j_conn=self.neo4j_conn,
                    data=batch,
                    crawl_timestamp=datetime.now().strftime('%Y%m%d_%H%M%S'),
                    source_type="crawler",
                    metrics={"ratings": 4.0}
                )
        
        # 并发执行所有批次
        tasks = [process_batch_with_control(batch, i) for i, batch in enumerate(batches)]
        batch_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 汇总结果
        results = []
        success_count = 0
        failed_count = 0
        
        for batch_result in batch_results:
            if isinstance(batch_result, Exception):
                logger.error(f"批次处理失败: {batch_result}")
                failed_count += batch_size  # 假设整个批次都失败
                continue
                
            results.extend(batch_result)
            for result in batch_result:
                if result.get("status") == "success":
                    success_count += 1
                else:
                    failed_count += 1
        
        end_time = time.time()
        duration = end_time - start_time
        
        result = {
            "batch_name": batch_name,
            "total_nodes": len(nodes),
            "success_count": success_count,
            "failed_count": failed_count,
            "duration_seconds": duration,
            "start_time": datetime.fromtimestamp(start_time).isoformat(),
            "end_time": datetime.fromtimestamp(end_time).isoformat(),
            "nodes_per_second": len(nodes) / duration if duration > 0 else 0
        }
        
        logger.info(f"改进异步批次 {batch_name} 完成: {success_count}/{len(nodes)} 成功, 耗时: {duration:.2f}秒")
        return result
        
    except Exception as e:
        logger.error(f"改进异步导入失败: {e}", exc_info=True)
        raise
```

### 4. 指数退避重试

**问题**: 固定重试间隔可能加剧限流
**解决方案**: 智能退避策略

```python
@retry(
    stop=stop_after_attempt(5),  # 增加重试次数
    wait=wait_exponential(multiplier=2, min=1, max=30),  # 指数退避
    retry=retry_if_exception_type((httpx.HTTPStatusError, httpx.RequestError)),
    before_sleep=lambda retry_state: logger.warning(f"API限流，第 {retry_state.attempt_number} 次重试，等待 {retry_state.next_action.sleep} 秒")
)
async def call_deepseek_with_retry(prompt: str, model_name: str = "default") -> str:
    # ... API调用代码 ...
```

### 5. 请求优先级和队列

**问题**: 所有请求同等优先级
**解决方案**: 实现请求队列和优先级

```python
class PriorityRequestQueue:
    def __init__(self, max_concurrent=3):
        self.queue = asyncio.PriorityQueue()
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.workers = []
        
    async def start_workers(self, num_workers=3):
        """启动工作协程"""
        for i in range(num_workers):
            worker = asyncio.create_task(self._worker(f"worker-{i}"))
            self.workers.append(worker)
    
    async def _worker(self, name):
        """工作协程，处理队列中的请求"""
        while True:
            try:
                priority, request_func, future = await self.queue.get()
                logger.debug(f"{name} 处理优先级 {priority} 的请求")
                
                async with self.semaphore:
                    try:
                        result = await request_func()
                        future.set_result(result)
                    except Exception as e:
                        future.set_exception(e)
                    finally:
                        self.queue.task_done()
                        
            except asyncio.CancelledError:
                break
    
    async def add_request(self, request_func, priority=1):
        """添加请求到队列"""
        future = asyncio.Future()
        await self.queue.put((priority, request_func, future))
        return await future

# 使用优先级队列
request_queue = PriorityRequestQueue(max_concurrent=3)
```

## 📊 预期改进效果

### 改进前 vs 改进后对比

| 指标 | 改进前 | 改进后 | 改进幅度 |
|------|--------|--------|----------|
| **并发控制** | 固定10个 | 动态3-6个 | 减少限流风险 |
| **请求间隔** | 无 | 0.5秒 | 避免突发请求 |
| **批次处理** | 串行 | 并发 | 提升2-3倍效率 |
| **重试策略** | 固定间隔 | 指数退避 | 减少重试冲突 |
| **预期速度** | 5882秒 | 2500-3000秒 | 提升50-60% |

### 关键改进点

1. **智能并发**: 根据API响应动态调整并发数
2. **平滑请求**: 避免突发请求触发限流
3. **真正异步**: 批次级别的并发处理
4. **优雅降级**: 遇到限流时自动降低请求频率
5. **监控反馈**: 实时监控成功率并调整策略

## 🎯 实施建议

### 阶段1: 基础改进
1. 实施智能并发控制
2. 添加请求间隔
3. 改进重试策略

### 阶段2: 架构优化
1. 实现真正的并发批处理
2. 添加请求队列和优先级
3. 实施监控和自动调优

### 阶段3: 高级优化
1. 实现请求缓存
2. 添加API健康检查
3. 实施熔断器模式

通过这些改进，你的异步处理应该能够在避免API限流的同时，发挥出真正的异步优势，预期性能提升50-60%。
