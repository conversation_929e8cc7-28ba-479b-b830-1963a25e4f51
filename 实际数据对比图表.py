#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于实际实验数据生成异步vs同步对比图表
"""

import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime

# 配置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def create_actual_comparison_charts():
    """基于实际数据创建对比图表"""
    
    # 实际实验数据
    batch_sizes = [10, 50, 100, 150, 200, 250]
    
    # 异步处理时间 (秒) - 实际测量数据
    async_times = [110.69, 513.75, 1324.55, 3273.08, 4422.49, 5882.45]
    
    # 同步处理时间 (秒) - 实际测量数据
    sync_times = [118, 556, 1207, 1569, 1909, 2167]
    
    # 处理速度 (节点/秒)
    async_speeds = [0.090, 0.097, 0.076, 0.046, 0.045, 0.042]
    sync_speeds = [0.085, 0.090, 0.083, 0.096, 0.105, 0.115]
    
    # 每节点处理时间
    async_per_node = [t/s for t, s in zip(async_times, batch_sizes)]
    sync_per_node = [t/s for t, s in zip(sync_times, batch_sizes)]
    
    # 创建2x2子图
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. 处理时间对比 (柱状图)
    x = np.arange(len(batch_sizes))
    width = 0.35
    
    bars1 = ax1.bar(x - width/2, async_times, width, label='异步处理', color='#FF6B6B', alpha=0.8)
    bars2 = ax1.bar(x + width/2, sync_times, width, label='同步处理', color='#5B9BD5', alpha=0.8)
    
    ax1.set_xlabel('批次大小 (个景点)')
    ax1.set_ylabel('处理时间 (秒)')
    ax1.set_title('异步 vs 同步处理时间对比 (实际数据)')
    ax1.set_xticks(x)
    ax1.set_xticklabels(batch_sizes)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 在柱子上添加数值标签
    for bar in bars1:
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{int(height)}s', ha='center', va='bottom', fontsize=8)
    
    for bar in bars2:
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{int(height)}s', ha='center', va='bottom', fontsize=8)
    
    # 2. 处理速度对比 (折线图)
    ax2.plot(batch_sizes, async_speeds, 'o-', label='异步处理', linewidth=3, markersize=8, color='#FF6B6B')
    ax2.plot(batch_sizes, sync_speeds, 's-', label='同步处理', linewidth=3, markersize=8, color='#5B9BD5')
    ax2.set_xlabel('批次大小 (个景点)')
    ax2.set_ylabel('处理速度 (节点/秒)')
    ax2.set_title('处理速度对比')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(0, 0.13)
    
    # 添加数值标签
    for i, (size, async_speed, sync_speed) in enumerate(zip(batch_sizes, async_speeds, sync_speeds)):
        ax2.annotate(f'{async_speed:.3f}', (size, async_speed), textcoords="offset points", 
                    xytext=(0,10), ha='center', fontsize=8, color='#FF6B6B')
        ax2.annotate(f'{sync_speed:.3f}', (size, sync_speed), textcoords="offset points", 
                    xytext=(0,-15), ha='center', fontsize=8, color='#5B9BD5')
    
    # 3. 每节点处理时间对比
    ax3.plot(batch_sizes, async_per_node, 'o-', label='异步处理', linewidth=3, markersize=8, color='#FF6B6B')
    ax3.plot(batch_sizes, sync_per_node, 's-', label='同步处理', linewidth=3, markersize=8, color='#5B9BD5')
    ax3.set_xlabel('批次大小 (个景点)')
    ax3.set_ylabel('每节点处理时间 (秒)')
    ax3.set_title('单节点处理时间对比')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 性能差异百分比 (柱状图)
    time_differences = [(a-s)/s*100 for a, s in zip(async_times, sync_times)]
    speed_differences = [(a-s)/s*100 for a, s in zip(async_speeds, sync_speeds)]
    
    x = np.arange(len(batch_sizes))
    width = 0.35
    
    # 根据正负值设置颜色
    time_colors = ['#70AD47' if diff < 0 else '#FF6B6B' for diff in time_differences]
    speed_colors = ['#70AD47' if diff > 0 else '#FF6B6B' for diff in speed_differences]
    
    bars1 = ax4.bar(x - width/2, time_differences, width, label='时间差异%', color=time_colors, alpha=0.8)
    bars2 = ax4.bar(x + width/2, speed_differences, width, label='速度差异%', color=speed_colors, alpha=0.8)
    
    ax4.set_xlabel('批次大小 (个景点)')
    ax4.set_ylabel('性能差异百分比 (%)')
    ax4.set_title('异步相对于同步的性能差异\n(负值表示异步更慢，正值表示异步更快)')
    ax4.set_xticks(x)
    ax4.set_xticklabels(batch_sizes)
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    ax4.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    
    # 添加数值标签
    for bar, diff in zip(bars1, time_differences):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + (5 if height > 0 else -10),
                f'{diff:.1f}%', ha='center', va='bottom' if height > 0 else 'top', 
                fontsize=8, fontweight='bold')
    
    for bar, diff in zip(bars2, speed_differences):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + (5 if height > 0 else -10),
                f'{diff:.1f}%', ha='center', va='bottom' if height > 0 else 'top', 
                fontsize=8, fontweight='bold')
    
    plt.tight_layout()
    
    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"实际数据异步vs同步对比_{timestamp}.png"
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"实际数据对比图表已保存: {filename}")
    
    return filename

def print_detailed_comparison():
    """打印详细对比分析"""
    print("\n" + "="*90)
    print("异步 vs 同步实际实验数据详细对比")
    print("="*90)
    
    batch_sizes = [10, 50, 100, 150, 200, 250]
    async_times = [110.69, 513.75, 1324.55, 3273.08, 4422.49, 5882.45]
    sync_times = [118, 556, 1207, 1569, 1909, 2167]
    async_speeds = [0.090, 0.097, 0.076, 0.046, 0.045, 0.042]
    sync_speeds = [0.085, 0.090, 0.083, 0.096, 0.105, 0.115]
    
    print(f"{'批次':<6} {'异步时间':<10} {'同步时间':<10} {'时间差异':<10} {'异步速度':<10} {'同步速度':<10} {'速度差异':<10} {'推荐':<8}")
    print("-" * 90)
    
    for i, size in enumerate(batch_sizes):
        time_diff = (async_times[i] - sync_times[i]) / sync_times[i] * 100
        speed_diff = (async_speeds[i] - sync_speeds[i]) / sync_speeds[i] * 100
        recommendation = "异步" if time_diff < 0 and abs(time_diff) < 20 else "同步"
        
        print(f"{size:<6} "
              f"{async_times[i]:<10.1f} "
              f"{sync_times[i]:<10.1f} "
              f"{time_diff:<10.1f}% "
              f"{async_speeds[i]:<10.3f} "
              f"{sync_speeds[i]:<10.3f} "
              f"{speed_diff:<10.1f}% "
              f"{recommendation:<8}")
    
    print("-" * 90)
    print("关键发现:")
    print("1. 小批次(≤50个): 异步略优，时间节省6-8%")
    print("2. 大批次(≥100个): 同步显著优于异步，时间节省9-63%")
    print("3. 同步处理扩展性更好，速度随批次增大而提升")
    print("4. 异步处理在大批次时存在严重性能瓶颈")
    print("="*90)

def main():
    """主函数"""
    print("基于实际实验数据生成异步vs同步对比图表...")
    
    # 生成图表
    filename = create_actual_comparison_charts()
    
    # 打印详细对比
    print_detailed_comparison()
    
    print(f"\n实验数据说明:")
    print("- 异步数据: 2025年7月23-24日实际测量")
    print("- 同步数据: 2025年7月24日实际测量")
    print("- 测试环境: Neo4j知识图谱导入")
    print("- 数据来源: 拉萨景点知识图谱数据")

if __name__ == "__main__":
    main()
