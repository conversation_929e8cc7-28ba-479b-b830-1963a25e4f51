#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的异步导入实现 - 专注实体处理，跳过关系推断
"""

import asyncio
import json
import logging
import os
import sys
import time
import random
from datetime import datetime
from typing import Dict, List, Any
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from neo4j_connection import Neo4jConnection
from knowledge_graph_updater import KnowledgeGraphUpdater
from text_processor import extract_relationships

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('simplified_async_import.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


class AdaptiveSemaphore:
    """自适应信号量 - 根据API响应动态调整并发数"""
    
    def __init__(self, initial_permits=3, min_permits=1, max_permits=6):
        self.semaphore = asyncio.Semaphore(initial_permits)
        self.current_permits = initial_permits
        self.min_permits = min_permits
        self.max_permits = max_permits
        self.success_count = 0
        self.error_count = 0
        self.last_adjustment = time.time()
        self.lock = asyncio.Lock()
        
    async def __aenter__(self):
        await self.semaphore.acquire()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        self.semaphore.release()
        
        # 记录成功/失败
        async with self.lock:
            if exc_type is None:
                self.success_count += 1
            else:
                self.error_count += 1
            
            # 每30秒或每20次请求后评估一次
            current_time = time.time()
            total_requests = self.success_count + self.error_count
            
            if (current_time - self.last_adjustment > 30) or (total_requests >= 20):
                await self._adjust_permits()
                self.last_adjustment = current_time
                self.success_count = 0
                self.error_count = 0
    
    async def _adjust_permits(self):
        """调整并发数"""
        if self.error_count > self.success_count:
            # 错误率高，减少并发
            new_permits = max(self.min_permits, self.current_permits - 1)
            if new_permits != self.current_permits:
                self.current_permits = new_permits
                self.semaphore = asyncio.Semaphore(self.current_permits)
                logger.warning(f"检测到高错误率，降低API并发至: {self.current_permits}")
                
        elif self.success_count > self.error_count * 3:
            # 成功率很高，适度增加并发
            new_permits = min(self.max_permits, self.current_permits + 1)
            if new_permits != self.current_permits:
                self.current_permits = new_permits
                self.semaphore = asyncio.Semaphore(self.current_permits)
                logger.info(f"成功率高，提升API并发至: {self.current_permits}")


async def simplified_process_chunk(neo4j_conn: Neo4jConnection, data: List[Dict], crawl_timestamp: str, source_type: str, metrics: Dict):
    """简化的处理函数 - 只处理实体，跳过关系推断"""
    if not data or not isinstance(data, list):
        logger.error("数据为空或格式无效，跳过处理")
        return []

    updater = KnowledgeGraphUpdater(neo4j_conn)
    results = []

    # 只处理实体，不推断关系
    for item in data:
        try:
            if not item.get("name"):
                logger.warning(f"跳过无效数据，缺少name: {item}")
                continue
                
            # 使用LLM增强实体数据
            processed_item = await extract_relationships(item)
            
            log_id = f"{processed_item['name']}_{crawl_timestamp}"
            weights = {"rules_valid": 1.0, "llm_valid": 0.8, "weight_valid": 0.9}
            reason = "Simplified async import with LLM enhancement"
            
            updater.update_knowledge_graph(processed_item, log_id, reason, weights)
            logger.info(f"成功处理实体: {processed_item['name']}")
            results.append({"name": processed_item["name"], "status": "success"})
            
        except Exception as e:
            logger.error(f"处理实体 {item.get('name', 'Unknown')} 失败: {e}", exc_info=True)
            results.append({"name": item.get("name", "Unknown"), "status": "failed", "error": str(e)})
            continue

    logger.info(f"简化处理完成: {len([r for r in results if r['status'] == 'success'])}/{len(data)} 成功")
    return results


class SimplifiedAsyncImporter:
    """简化的异步导入器"""
    
    def __init__(self, neo4j_conn: Neo4jConnection):
        self.neo4j_conn = neo4j_conn
        self.updater = KnowledgeGraphUpdater(neo4j_conn)
        self.results = {}
        
    async def import_batch_simplified(self, nodes: List[Dict], batch_name: str, batch_size: int = 20) -> Dict:
        """简化的异步导入 - 专注实体处理"""
        logger.info(f"开始简化异步导入: {batch_name}, 节点数: {len(nodes)}")
        start_time = time.time()
        
        try:
            # 将节点分成更大的批次
            batches = [nodes[i:i + batch_size] for i in range(0, len(nodes), batch_size)]
            logger.info(f"分成 {len(batches)} 个子批次，每批次最多 {batch_size} 个节点")
            
            # 限制批次级别的并发
            batch_semaphore = asyncio.Semaphore(3)  # 最多3个批次并发
            
            async def process_batch_with_control(batch, batch_idx):
                async with batch_semaphore:
                    logger.info(f"开始处理批次 {batch_idx + 1}/{len(batches)}")
                    
                    # 批次间添加随机延迟
                    delay = random.uniform(0.1, 0.3)
                    await asyncio.sleep(delay)
                    
                    return await simplified_process_chunk(
                        neo4j_conn=self.neo4j_conn,
                        data=batch,
                        crawl_timestamp=datetime.now().strftime('%Y%m%d_%H%M%S'),
                        source_type="crawler",
                        metrics={"ratings": 4.0}
                    )
            
            # 并发执行所有批次
            tasks = [process_batch_with_control(batch, i) for i, batch in enumerate(batches)]
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 汇总结果
            results = []
            success_count = 0
            failed_count = 0
            
            for i, batch_result in enumerate(batch_results):
                if isinstance(batch_result, Exception):
                    logger.error(f"批次 {i+1} 处理失败: {batch_result}")
                    failed_count += len(batches[i])
                    continue
                    
                if batch_result:
                    results.extend(batch_result)
                    for result in batch_result:
                        if result.get("status") == "success":
                            success_count += 1
                        else:
                            failed_count += 1
            
            end_time = time.time()
            duration = end_time - start_time
            
            result = {
                "batch_name": batch_name,
                "total_nodes": len(nodes),
                "success_count": success_count,
                "failed_count": failed_count,
                "duration_seconds": duration,
                "start_time": datetime.fromtimestamp(start_time).isoformat(),
                "end_time": datetime.fromtimestamp(end_time).isoformat(),
                "nodes_per_second": len(nodes) / duration if duration > 0 else 0,
                "improvement_type": "simplified_async"
            }
            
            logger.info(f"简化异步批次 {batch_name} 完成: {success_count}/{len(nodes)} 成功, 耗时: {duration:.2f}秒")
            logger.info(f"处理速度: {result['nodes_per_second']:.3f} 节点/秒")
            
            return result
            
        except Exception as e:
            logger.error(f"简化异步导入失败: {e}", exc_info=True)
            raise
    
    async def run_simplified_experiment(self, json_file_path: str, batch_sizes: List[int]):
        """运行简化的异步实验"""
        logger.info(f"开始简化异步导入实验，批次大小: {batch_sizes}")
        
        # 读取JSON数据
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            nodes = data.get("nodes", [])
            logger.info(f"读取到 {len(nodes)} 个节点")
        except Exception as e:
            logger.error(f"读取JSON文件失败: {e}")
            return
        
        # 去重
        seen_names = set()
        unique_nodes = []
        for node in nodes:
            if node["name"] not in seen_names:
                seen_names.add(node["name"])
                unique_nodes.append(node)
        logger.info(f"去重后节点数: {len(unique_nodes)}")
        
        # 按批次大小进行实验
        experiment_results = []
        
        for batch_size in batch_sizes:
            logger.info(f"\n{'='*50}")
            logger.info(f"开始测试批次大小: {batch_size}")
            logger.info(f"{'='*50}")
            
            # 清空数据库
            try:
                self.neo4j_conn.clear_database()
                logger.info("数据库已清空")
            except Exception as e:
                logger.error(f"清空数据库失败: {e}")
                continue
            
            # 选择前N个节点
            batch_nodes = unique_nodes[:batch_size]
            batch_name = f"simplified_async_batch_{batch_size}_nodes"
            
            # 执行简化的异步导入
            try:
                result = await self.import_batch_simplified(batch_nodes, batch_name)
                experiment_results.append(result)
                
                # 记录结果
                logger.info(f"批次 {batch_size} 结果:")
                logger.info(f"  - 成功: {result['success_count']}/{result['total_nodes']}")
                logger.info(f"  - 耗时: {result['duration_seconds']:.2f} 秒")
                logger.info(f"  - 速度: {result['nodes_per_second']:.3f} 节点/秒")
                
            except Exception as e:
                logger.error(f"批次 {batch_size} 执行失败: {e}")
                continue
            
            # 等待一段时间再进行下一批次
            await asyncio.sleep(2)
        
        # 保存实验结果
        self.save_experiment_results(experiment_results)
        
        # 打印汇总报告
        self.print_summary_report(experiment_results)
        
        return experiment_results
    
    def save_experiment_results(self, results: List[Dict]):
        """保存实验结果到JSON文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"simplified_async_results_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            logger.info(f"实验结果已保存到: {filename}")
        except Exception as e:
            logger.error(f"保存实验结果失败: {e}")
    
    def print_summary_report(self, results: List[Dict]):
        """打印汇总报告"""
        logger.info(f"\n{'='*60}")
        logger.info("简化异步导入实验汇总报告")
        logger.info(f"{'='*60}")
        
        for result in results:
            batch_size = result['total_nodes']
            duration = result['duration_seconds']
            speed = result['nodes_per_second']
            success_rate = result['success_count'] / result['total_nodes'] * 100
            
            logger.info(f"{batch_size:>3}个节点: {duration:>7.1f}秒 | 速度: {speed:.3f}节点/秒 | 成功率: {success_rate:.1f}%")
        
        logger.info(f"{'='*60}")
    
    def close(self):
        """关闭连接"""
        if hasattr(self, 'neo4j_conn'):
            self.neo4j_conn.close()


async def main():
    """主函数"""
    importer = None
    try:
        # 连接Neo4j
        neo4j_config = Config.get_neo4j_config()
        neo4j_conn = Neo4jConnection(
            uri=neo4j_config["uri"],
            user=neo4j_config["user"],
            password=neo4j_config["password"]
        )
        neo4j_conn.verify_connectivity()
        logger.info("成功连接到Neo4j数据库")
        
        # 创建简化的导入器
        importer = SimplifiedAsyncImporter(neo4j_conn)
        
        # 设置测试批次大小
        batch_sizes = [10, 50, 100, 150, 200, 250]
        
        # JSON文件路径
        json_file_path = os.path.join(os.path.dirname(__file__), "data", "lhasa_knowledge_graph.json")
        
        # 运行简化实验
        results = await importer.run_simplified_experiment(json_file_path, batch_sizes)
        
        logger.info("简化异步导入实验完成！")
        
    except Exception as e:
        logger.error(f"主程序错误: {e}", exc_info=True)
    finally:
        if importer:
            importer.close()


if __name__ == "__main__":
    asyncio.run(main())
