#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恢复异步导入实验脚本
用于明天继续未完成的批次实验
"""

import asyncio
import json
import logging
import os
import sys
import time
from datetime import datetime
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from batch_async_import import BatchAsyncImporter
from config import Config
from neo4j_connection import Neo4jConnection

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('resume_experiment.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def analyze_completed_batches(log_file="batch_async_import.log"):
    """分析已完成的批次"""
    completed_batches = set()
    
    if not os.path.exists(log_file):
        logger.warning(f"日志文件不存在: {log_file}")
        return completed_batches
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找已完成的批次
        import re
        pattern = r"批次 batch_(\d+)_nodes 完成:"
        matches = re.findall(pattern, content)
        
        for match in matches:
            completed_batches.add(int(match))
        
        logger.info(f"已完成的批次大小: {sorted(completed_batches)}")
        return completed_batches
        
    except Exception as e:
        logger.error(f"分析日志文件失败: {e}")
        return completed_batches

async def resume_experiment():
    """恢复实验"""
    logger.info("开始恢复异步导入实验")
    logger.info("="*60)
    
    # 分析已完成的批次
    completed_batches = analyze_completed_batches()
    
    # 定义所有需要的批次
    all_batches = [10, 50, 100, 150, 200, 250]
    
    # 找出未完成的批次
    remaining_batches = []
    for batch_size in all_batches:
        if batch_size not in completed_batches:
            remaining_batches.append(batch_size)
    
    if not remaining_batches:
        logger.info("所有批次已完成！")
        return
    
    logger.info(f"需要完成的批次: {remaining_batches}")
    
    # 连接Neo4j
    try:
        neo4j_config = Config.get_neo4j_config()
        neo4j_conn = Neo4jConnection(
            uri=neo4j_config["uri"],
            user=neo4j_config["user"],
            password=neo4j_config["password"]
        )
        neo4j_conn.verify_connectivity()
        logger.info("成功连接到Neo4j数据库")
    except Exception as e:
        logger.error(f"连接Neo4j失败: {e}")
        return
    
    # 创建导入器
    importer = BatchAsyncImporter(neo4j_conn)
    
    try:
        # JSON文件路径
        json_file_path = os.path.join(os.path.dirname(__file__), "data", "lhasa_knowledge_graph.json")
        
        # 运行剩余的批次
        results = await importer.run_batch_experiment(json_file_path, remaining_batches)
        
        logger.info("恢复的实验完成！")
        
        # 生成完整的实验报告
        generate_final_report()
        
    except Exception as e:
        logger.error(f"恢复实验失败: {e}")
    finally:
        importer.close()

def generate_final_report():
    """生成最终实验报告"""
    logger.info("生成最终实验报告...")
    
    try:
        # 查找最新的结果文件
        result_files = [f for f in os.listdir('.') if f.startswith('async_batch_import_results_') and f.endswith('.json')]
        
        if result_files:
            latest_file = max(result_files, key=lambda x: os.path.getmtime(x))
            logger.info(f"找到结果文件: {latest_file}")
            
            with open(latest_file, 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            # 生成汇总报告
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = f"final_async_experiment_report_{timestamp}.md"
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("# Neo4j异步导入实验最终报告\n\n")
                f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                f.write("## 实验结果汇总\n\n")
                f.write("| 批次大小 | 成功数/总数 | 成功率 | 耗时(秒) | 速度(节点/秒) |\n")
                f.write("|---------|------------|--------|----------|---------------|\n")
                
                for result in results:
                    f.write(f"| {result['total_nodes']} | "
                           f"{result['success_count']}/{result['total_nodes']} | "
                           f"{result['success_count']/result['total_nodes']*100:.1f}% | "
                           f"{result['duration_seconds']:.2f} | "
                           f"{result['nodes_per_second']:.2f} |\n")
                
                # 计算平均值
                avg_success_rate = sum(r['success_count']/r['total_nodes']*100 for r in results) / len(results)
                avg_speed = sum(r['nodes_per_second'] for r in results) / len(results)
                total_time = sum(r['duration_seconds'] for r in results)
                total_nodes = sum(r['total_nodes'] for r in results)
                
                f.write(f"\n## 统计汇总\n\n")
                f.write(f"- **平均成功率**: {avg_success_rate:.1f}%\n")
                f.write(f"- **平均处理速度**: {avg_speed:.2f} 节点/秒\n")
                f.write(f"- **总处理时间**: {total_time:.2f} 秒 ({total_time/60:.1f} 分钟)\n")
                f.write(f"- **总处理节点**: {total_nodes} 个\n")
                f.write(f"- **实验批次数**: {len(results)} 个\n")
            
            logger.info(f"最终报告已生成: {report_file}")
        else:
            logger.warning("未找到实验结果文件")
            
    except Exception as e:
        logger.error(f"生成最终报告失败: {e}")

def main():
    """主函数"""
    print("Neo4j异步导入实验恢复工具")
    print("="*50)
    
    choice = input("请选择操作:\n1. 恢复未完成的实验\n2. 生成最终报告\n3. 查看实验状态\n请输入选择 (1-3): ")
    
    if choice == "1":
        asyncio.run(resume_experiment())
    elif choice == "2":
        generate_final_report()
    elif choice == "3":
        completed_batches = analyze_completed_batches()
        all_batches = [10, 50, 100, 150, 200, 250]
        remaining = [b for b in all_batches if b not in completed_batches]
        print(f"已完成批次: {sorted(completed_batches)}")
        print(f"剩余批次: {remaining}")
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
