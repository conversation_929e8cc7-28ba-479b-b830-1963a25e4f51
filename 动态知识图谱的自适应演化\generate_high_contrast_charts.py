#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修改原图颜色方案 - 提高对比度，保持原有布局
"""

import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from datetime import datetime
import matplotlib.patches as patches

# 设置中文字体和高DPI
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300

# 定义高对比度颜色方案 - 基于原图但提高对比度
colors = {
    'async_color': '#0066CC',        # 深蓝色 - 异步处理
    'sync_color': '#FF6600',         # 橙红色 - 同步处理
    'improvement_color': '#009900',   # 深绿色 - 改进效果
    'async_line': '#003399',         # 更深蓝色 - 异步线条
    'sync_line': '#CC3300',          # 深红色 - 同步线条
    'grid_color': '#CCCCCC',         # 浅灰色网格
    'text_color': '#000000'          # 黑色文字
}

# 实验数据 - 基于原图数据
data = {
    'batch_sizes': [10, 50, 100, 150, 200, 250],
    'async_times': [70.50, 238.30, 761.39, 1177.43, 1366.78, 1456.84],
    'sync_times': [118, 556, 1207, 1569, 1909, 2167],
    'async_speeds': [0.142, 0.210, 0.131, 0.127, 0.146, 0.172],
    'sync_speeds': [0.085, 0.090, 0.083, 0.096, 0.105, 0.115],
    'improvements': [40.3, 57.1, 36.9, 25.0, 28.4, 32.8]
}

def create_original_layout_high_contrast():
    """重现原图布局，仅修改颜色提高对比度"""

    # 创建2x3的子图布局，与原图相同
    fig = plt.figure(figsize=(15, 10))
    fig.patch.set_facecolor('white')

    # 1. 处理时间对比 (柱状图) - 左上
    ax1 = plt.subplot(2, 3, 1)
    x = np.arange(len(data['batch_sizes']))
    width = 0.35

    bars1 = ax1.bar(x - width/2, data['async_times'], width,
                    label='异步处理', color=colors['async_color'],
                    alpha=0.8, edgecolor='black', linewidth=0.8)
    bars2 = ax1.bar(x + width/2, data['sync_times'], width,
                    label='同步处理', color=colors['sync_color'],
                    alpha=0.8, edgecolor='black', linewidth=0.8)

    ax1.set_xlabel('批次大小 (个节点)', fontsize=11)
    ax1.set_ylabel('处理时间 (秒)', fontsize=11)
    ax1.set_title('处理时间对比 (含节点数量)', fontsize=12, fontweight='bold')
    ax1.set_xticks(x)
    ax1.set_xticklabels(data['batch_sizes'])
    ax1.legend(fontsize=10)
    ax1.grid(True, alpha=0.3, color=colors['grid_color'])
    
    # 2. 处理速度对比 (折线图)
    ax2 = plt.subplot(2, 3, 2)
    ax2.plot(data['batch_sizes'], data['async_speeds'], 
             marker='o', linewidth=3, markersize=8, 
             color=colors['async_primary'], label='异步处理速度',
             markerfacecolor='white', markeredgewidth=2)
    ax2.plot(data['batch_sizes'], data['sync_speeds'], 
             marker='s', linewidth=3, markersize=8,
             color=colors['sync_primary'], label='同步处理速度',
             markerfacecolor='white', markeredgewidth=2)
    
    ax2.set_xlabel('批次大小 (个节点)', fontsize=12, fontweight='bold')
    ax2.set_ylabel('处理速度 (节点/秒)', fontsize=12, fontweight='bold')
    ax2.set_title('处理速度对比 (含节点数量)', fontsize=14, fontweight='bold')
    ax2.legend(fontsize=11, frameon=True, fancybox=True, shadow=True)
    ax2.grid(True, alpha=0.3, color=colors['grid'])
    ax2.set_facecolor(colors['background'])
    
    # 3. 性能提升百分比 (柱状图)
    ax3 = plt.subplot(2, 3, 3)
    bars3 = ax3.bar(data['batch_sizes'], data['improvements'], 
                    color=colors['improvement'], alpha=0.8,
                    edgecolor='black', linewidth=0.5)
    
    ax3.set_xlabel('批次大小 (个节点)', fontsize=12, fontweight='bold')
    ax3.set_ylabel('性能提升百分比 (%)', fontsize=12, fontweight='bold')
    ax3.set_title('异步处理性能提升幅度', fontsize=14, fontweight='bold')
    ax3.grid(True, alpha=0.3, color=colors['grid'])
    ax3.set_facecolor(colors['background'])
    
    # 添加百分比标签
    for bar in bars3:
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{height:.1f}%', ha='center', va='bottom', 
                fontsize=10, fontweight='bold')
    
    # 4. 每节点处理时间对比
    ax4 = plt.subplot(2, 3, 4)
    async_per_node = [t/s for t, s in zip(data['async_times'], data['batch_sizes'])]
    sync_per_node = [t/s for t, s in zip(data['sync_times'], data['batch_sizes'])]
    
    ax4.plot(data['batch_sizes'], async_per_node, 
             marker='o', linewidth=3, markersize=8,
             color=colors['async_secondary'], label='异步处理每节点时间',
             markerfacecolor='white', markeredgewidth=2)
    ax4.plot(data['batch_sizes'], sync_per_node, 
             marker='s', linewidth=3, markersize=8,
             color=colors['sync_secondary'], label='同步处理每节点时间',
             markerfacecolor='white', markeredgewidth=2)
    
    ax4.set_xlabel('批次大小 (个节点)', fontsize=12, fontweight='bold')
    ax4.set_ylabel('每节点处理时间 (秒)', fontsize=12, fontweight='bold')
    ax4.set_title('单节点处理时间对比', fontsize=14, fontweight='bold')
    ax4.legend(fontsize=11, frameon=True, fancybox=True, shadow=True)
    ax4.grid(True, alpha=0.3, color=colors['grid'])
    ax4.set_facecolor(colors['background'])
    
    # 5. 累积处理时间对比
    ax5 = plt.subplot(2, 3, 5)
    cumulative_async = np.cumsum(data['async_times'])
    cumulative_sync = np.cumsum(data['sync_times'])
    
    ax5.plot(data['batch_sizes'], cumulative_async, 
             marker='o', linewidth=3, markersize=8,
             color=colors['async_primary'], label='异步累积时间',
             markerfacecolor='white', markeredgewidth=2)
    ax5.plot(data['batch_sizes'], cumulative_sync, 
             marker='s', linewidth=3, markersize=8,
             color=colors['sync_primary'], label='同步累积时间',
             markerfacecolor='white', markeredgewidth=2)
    
    ax5.set_xlabel('批次大小 (个节点)', fontsize=12, fontweight='bold')
    ax5.set_ylabel('累积处理时间 (秒)', fontsize=12, fontweight='bold')
    ax5.set_title('累积处理时间对比', fontsize=14, fontweight='bold')
    ax5.legend(fontsize=11, frameon=True, fancybox=True, shadow=True)
    ax5.grid(True, alpha=0.3, color=colors['grid'])
    ax5.set_facecolor(colors['background'])
    
    # 6. 综合性能雷达图
    ax6 = plt.subplot(2, 3, 6, projection='polar')
    
    # 雷达图数据 (标准化到0-1)
    categories = ['处理速度', '时间效率', '资源利用', '扩展性', '稳定性']
    async_scores = [0.8, 0.9, 0.85, 0.8, 1.0]  # 异步处理评分
    sync_scores = [0.6, 0.6, 0.7, 0.9, 0.9]    # 同步处理评分
    
    angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
    async_scores += async_scores[:1]  # 闭合图形
    sync_scores += sync_scores[:1]
    angles += angles[:1]
    
    ax6.plot(angles, async_scores, 'o-', linewidth=3, 
             color=colors['async_primary'], label='异步处理')
    ax6.fill(angles, async_scores, alpha=0.25, color=colors['async_primary'])
    
    ax6.plot(angles, sync_scores, 's-', linewidth=3,
             color=colors['sync_primary'], label='同步处理')
    ax6.fill(angles, sync_scores, alpha=0.25, color=colors['sync_primary'])
    
    ax6.set_xticks(angles[:-1])
    ax6.set_xticklabels(categories, fontsize=11)
    ax6.set_ylim(0, 1)
    ax6.set_title('综合性能雷达图', fontsize=14, fontweight='bold', pad=20)
    ax6.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0), fontsize=11)
    ax6.grid(True, alpha=0.3)
    
    # 调整布局
    plt.tight_layout(pad=3.0)
    
    # 添加总标题
    fig.suptitle('异步处理 vs 同步处理 - 完整性能对比分析', 
                fontsize=16, fontweight='bold', y=0.98)
    
    # 保存图表
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f'高对比度异步vs同步完整对比_{timestamp}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    print(f"高对比度图表已保存为: {filename}")
    plt.close()  # 关闭图形以释放内存

    return filename

if __name__ == "__main__":
    create_high_contrast_charts()
