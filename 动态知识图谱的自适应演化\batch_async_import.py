#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分组异步导入Neo4j数据库实验
按照10、50、100、150、200、250个景点分组进行异步导入，并记录每个组别的时间
"""

import asyncio
import json
import logging
import os
import sys
import time
from datetime import datetime
from typing import Dict, List, Any
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from neo4j_connection import Neo4jConnection
from knowledge_graph_updater import KnowledgeGraphUpdater
from text_processor import process_json_chunk, reset_database

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('batch_async_import.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BatchAsyncImporter:
    def __init__(self, neo4j_conn: Neo4jConnection):
        self.neo4j_conn = neo4j_conn
        self.updater = KnowledgeGraphUpdater(neo4j_conn)
        self.results = {}
        
    async def import_batch_async(self, nodes: List[Dict], batch_name: str, batch_size: int = 3) -> Dict:
        """异步导入一批节点数据"""
        logger.info(f"开始异步导入批次: {batch_name}, 节点数: {len(nodes)}")
        start_time = time.time()
        
        try:
            # 分小批次处理以避免内存问题
            results = []
            for i in range(0, len(nodes), batch_size):
                batch = nodes[i:i + batch_size]
                logger.info(f"处理子批次 {i//batch_size + 1}/{(len(nodes) + batch_size - 1)//batch_size}")
                
                batch_result = await process_json_chunk(
                    neo4j_conn=self.neo4j_conn,
                    data=batch,
                    crawl_timestamp="2025-06-19T20:46:39.861593",
                    source_type="crawler",
                    metrics={"ratings": 4.0}
                )
                results.extend(batch_result)
                
                # 添加小延迟避免过载
                await asyncio.sleep(0.1)
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 统计结果
            success_count = sum(1 for r in results if r.get("status") == "success")
            failed_count = len(results) - success_count
            
            result = {
                "batch_name": batch_name,
                "total_nodes": len(nodes),
                "success_count": success_count,
                "failed_count": failed_count,
                "duration_seconds": duration,
                "start_time": datetime.fromtimestamp(start_time).isoformat(),
                "end_time": datetime.fromtimestamp(end_time).isoformat(),
                "nodes_per_second": len(nodes) / duration if duration > 0 else 0
            }
            
            logger.info(f"批次 {batch_name} 完成: {success_count}/{len(nodes)} 成功, 耗时: {duration:.2f}秒")
            return result
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            logger.error(f"批次 {batch_name} 导入失败: {e}", exc_info=True)
            return {
                "batch_name": batch_name,
                "total_nodes": len(nodes),
                "success_count": 0,
                "failed_count": len(nodes),
                "duration_seconds": duration,
                "error": str(e),
                "start_time": datetime.fromtimestamp(start_time).isoformat(),
                "end_time": datetime.fromtimestamp(end_time).isoformat()
            }

    async def run_batch_experiment(self, json_file_path: str, batch_sizes: List[int]):
        """运行分批导入实验"""
        logger.info(f"开始分批异步导入实验，批次大小: {batch_sizes}")
        
        # 读取JSON数据
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            nodes = data.get("nodes", [])
            logger.info(f"读取到 {len(nodes)} 个节点")
        except Exception as e:
            logger.error(f"读取JSON文件失败: {e}")
            return
        
        # 去重
        seen_names = set()
        unique_nodes = []
        for node in nodes:
            if node["name"] not in seen_names:
                seen_names.add(node["name"])
                unique_nodes.append(node)
        logger.info(f"去重后节点数: {len(unique_nodes)}")
        
        # 按批次大小进行实验
        experiment_results = []
        
        for batch_size in batch_sizes:
            if batch_size > len(unique_nodes):
                logger.warning(f"批次大小 {batch_size} 超过总节点数 {len(unique_nodes)}，跳过")
                continue
                
            logger.info(f"\n{'='*50}")
            logger.info(f"开始批次实验: {batch_size} 个节点")
            logger.info(f"{'='*50}")
            
            # 清空数据库
            try:
                self.neo4j_conn.clear_database()
                await reset_database(self.neo4j_conn)
                logger.info("数据库已清空并重置")
            except Exception as e:
                logger.error(f"清空数据库失败: {e}")
                continue
            
            # 选择前N个节点
            batch_nodes = unique_nodes[:batch_size]
            batch_name = f"batch_{batch_size}_nodes"
            
            # 执行异步导入
            result = await self.import_batch_async(batch_nodes, batch_name)
            experiment_results.append(result)
            
            # 记录结果
            logger.info(f"批次 {batch_size} 结果:")
            logger.info(f"  - 成功: {result['success_count']}/{result['total_nodes']}")
            logger.info(f"  - 耗时: {result['duration_seconds']:.2f} 秒")
            logger.info(f"  - 速度: {result['nodes_per_second']:.2f} 节点/秒")
            
            # 等待一段时间再进行下一批次
            await asyncio.sleep(2)
        
        # 保存实验结果
        self.save_experiment_results(experiment_results)
        
        # 打印汇总报告
        self.print_summary_report(experiment_results)
        
        return experiment_results

    def save_experiment_results(self, results: List[Dict]):
        """保存实验结果到文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"async_batch_import_results_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            logger.info(f"实验结果已保存到: {filename}")
        except Exception as e:
            logger.error(f"保存实验结果失败: {e}")

    def print_summary_report(self, results: List[Dict]):
        """打印汇总报告"""
        logger.info(f"\n{'='*60}")
        logger.info("异步批次导入实验汇总报告")
        logger.info(f"{'='*60}")
        
        for result in results:
            logger.info(f"批次大小: {result['total_nodes']:>3} | "
                       f"成功率: {result['success_count']}/{result['total_nodes']} "
                       f"({result['success_count']/result['total_nodes']*100:.1f}%) | "
                       f"耗时: {result['duration_seconds']:>6.2f}s | "
                       f"速度: {result['nodes_per_second']:>5.2f} 节点/秒")
        
        logger.info(f"{'='*60}")

    def close(self):
        """关闭连接"""
        if hasattr(self, 'updater'):
            self.updater.close()

async def main():
    """主函数"""
    importer = None
    try:
        # 连接Neo4j
        neo4j_config = Config.get_neo4j_config()
        neo4j_conn = Neo4jConnection(
            uri=neo4j_config["uri"],
            user=neo4j_config["user"],
            password=neo4j_config["password"]
        )
        neo4j_conn.verify_connectivity()
        logger.info("成功连接到Neo4j数据库")
        
        # 创建导入器
        importer = BatchAsyncImporter(neo4j_conn)
        
        # 设置批次大小 - 只运行昨天未完成的批次
        batch_sizes = [150, 200, 250]
        
        # JSON文件路径
        json_file_path = os.path.join(os.path.dirname(__file__), "data", "lhasa_knowledge_graph.json")
        
        # 运行实验
        results = await importer.run_batch_experiment(json_file_path, batch_sizes)
        
        logger.info("异步批次导入实验完成！")
        
    except Exception as e:
        logger.error(f"主程序错误: {e}", exc_info=True)
    finally:
        if importer:
            importer.close()

if __name__ == "__main__":
    asyncio.run(main())
