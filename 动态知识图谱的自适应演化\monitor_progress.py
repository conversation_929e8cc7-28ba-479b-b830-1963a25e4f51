#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控异步导入实验进度
"""

import re
import time
import os
from datetime import datetime

def monitor_log_file(log_file_path="batch_async_import.log"):
    """监控日志文件，提取批次完成信息"""
    if not os.path.exists(log_file_path):
        print(f"日志文件不存在: {log_file_path}")
        return
    
    print("开始监控异步导入实验进度...")
    print("="*60)
    
    completed_batches = []
    current_batch = None
    batch_start_time = None
    
    try:
        with open(log_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for line in lines:
            # 检测批次开始
            if "开始批次实验:" in line:
                match = re.search(r"开始批次实验: (\d+) 个节点", line)
                if match:
                    current_batch = int(match.group(1))
                    timestamp = line.split(" - ")[0]
                    batch_start_time = datetime.strptime(timestamp, "%Y-%m-%d %H:%M:%S,%f")
                    print(f"检测到批次开始: {current_batch} 个节点 - {timestamp}")
            
            # 检测批次完成
            if "批次" in line and "完成:" in line and "成功" in line:
                match = re.search(r"批次 (\w+) 完成: (\d+)/(\d+) 成功, 耗时: ([\d.]+)秒", line)
                if match:
                    batch_name = match.group(1)
                    success_count = int(match.group(2))
                    total_count = int(match.group(3))
                    duration = float(match.group(4))
                    timestamp = line.split(" - ")[0]
                    end_time = datetime.strptime(timestamp, "%Y-%m-%d %H:%M:%S,%f")
                    
                    batch_info = {
                        "batch_name": batch_name,
                        "total_nodes": total_count,
                        "success_count": success_count,
                        "failed_count": total_count - success_count,
                        "duration_seconds": duration,
                        "nodes_per_second": total_count / duration if duration > 0 else 0,
                        "end_time": timestamp,
                        "success_rate": success_count / total_count * 100 if total_count > 0 else 0
                    }
                    
                    completed_batches.append(batch_info)
                    print(f"✓ 批次完成: {batch_name}")
                    print(f"  - 节点数: {total_count}")
                    print(f"  - 成功: {success_count}/{total_count} ({batch_info['success_rate']:.1f}%)")
                    print(f"  - 耗时: {duration:.2f} 秒")
                    print(f"  - 速度: {batch_info['nodes_per_second']:.2f} 节点/秒")
                    print(f"  - 完成时间: {timestamp}")
                    print("-" * 40)
        
        # 显示汇总信息
        if completed_batches:
            print("\n" + "="*60)
            print("已完成批次汇总:")
            print("="*60)
            print(f"{'批次名称':<20} {'节点数':<8} {'成功率':<10} {'耗时(秒)':<10} {'速度(节点/秒)':<12}")
            print("-" * 60)
            
            for batch in completed_batches:
                print(f"{batch['batch_name']:<20} "
                      f"{batch['total_nodes']:<8} "
                      f"{batch['success_rate']:<10.1f}% "
                      f"{batch['duration_seconds']:<10.2f} "
                      f"{batch['nodes_per_second']:<12.2f}")
            
            print("-" * 60)
            print(f"总计完成批次: {len(completed_batches)}")
            
            if len(completed_batches) > 1:
                avg_success_rate = sum(b['success_rate'] for b in completed_batches) / len(completed_batches)
                avg_speed = sum(b['nodes_per_second'] for b in completed_batches) / len(completed_batches)
                print(f"平均成功率: {avg_success_rate:.1f}%")
                print(f"平均速度: {avg_speed:.2f} 节点/秒")
        else:
            print("暂无完成的批次")
            
        # 检查当前进度
        print("\n" + "="*60)
        print("当前进度分析:")
        print("="*60)
        
        # 统计处理的子批次
        sub_batch_count = 0
        for line in lines:
            if "处理子批次" in line:
                match = re.search(r"处理子批次 (\d+)/(\d+)", line)
                if match:
                    current_sub = int(match.group(1))
                    total_sub = int(match.group(2))
                    sub_batch_count = max(sub_batch_count, current_sub)
        
        if sub_batch_count > 0:
            print(f"当前正在处理子批次: {sub_batch_count}")
        
        # 统计成功处理的实体
        success_entities = []
        for line in lines[-50:]:  # 查看最后50行
            if "成功处理实体:" in line:
                match = re.search(r"成功处理实体: (.+)", line)
                if match:
                    entity_name = match.group(1)
                    success_entities.append(entity_name)
        
        if success_entities:
            print(f"最近成功处理的实体: {', '.join(success_entities[-5:])}")
        
        print("="*60)
        
    except Exception as e:
        print(f"监控过程中发生错误: {e}")

def main():
    """主函数"""
    monitor_log_file()

if __name__ == "__main__":
    main()
