#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本 - 使用小批次验证功能
"""

import asyncio
import json
import logging
import os
import sys
import time
from datetime import datetime
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from neo4j_connection import Neo4jConnection
from knowledge_graph_updater import KnowledgeGraphUpdater

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class QuickTester:
    def __init__(self):
        self.neo4j_conn = None
        
    def setup_connection(self):
        """设置Neo4j连接"""
        try:
            neo4j_config = Config.get_neo4j_config()
            self.neo4j_conn = Neo4jConnection(
                uri=neo4j_config["uri"],
                user=neo4j_config["user"],
                password=neo4j_config["password"]
            )
            self.neo4j_conn.verify_connectivity()
            logger.info("成功连接到Neo4j数据库")
            return True
        except Exception as e:
            logger.error(f"连接Neo4j失败: {e}")
            return False

    async def test_async_import(self, nodes: List[Dict], batch_name: str) -> Dict:
        """测试异步导入"""
        logger.info(f"开始异步导入测试: {batch_name}, 节点数: {len(nodes)}")
        start_time = time.time()
        
        try:
            updater = KnowledgeGraphUpdater(self.neo4j_conn)
            success_count = 0
            failed_count = 0
            
            # 简化的异步处理 - 不调用LLM
            for node in nodes:
                try:
                    processed_node = {
                        "name": node.get("name", ""),
                        "location": node.get("location", "拉萨市"),
                        "address": node.get("address", ""),
                        "description": node.get("description", f"Description for {node.get('name', 'Unknown')}"),
                        "pub_timestamp": node.get("pub_timestamp", datetime.now().isoformat()),
                        "ranking": node.get("ranking", ""),
                        "visitor_percentage": node.get("visitor_percentage", ""),
                        "source_type": "crawler",
                        "metrics": {"ratings": 4.0}
                    }
                    
                    log_id = f"{processed_node['name']}_async_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    weights = {"rules_valid": 1.0, "llm_valid": 0.8, "weight_valid": 0.9}
                    reason = "Async test import"
                    
                    updater.update_knowledge_graph(processed_node, log_id, reason, weights)
                    success_count += 1
                    logger.debug(f"成功处理: {processed_node['name']}")
                    
                except Exception as e:
                    failed_count += 1
                    logger.error(f"处理失败: {node.get('name', 'Unknown')}, 错误: {e}")
            
            updater.close()
            end_time = time.time()
            duration = end_time - start_time
            
            result = {
                "batch_name": batch_name,
                "total_nodes": len(nodes),
                "success_count": success_count,
                "failed_count": failed_count,
                "duration_seconds": duration,
                "nodes_per_second": len(nodes) / duration if duration > 0 else 0,
                "type": "async"
            }
            
            logger.info(f"异步测试完成: {success_count}/{len(nodes)} 成功, 耗时: {duration:.2f}秒")
            return result
            
        except Exception as e:
            logger.error(f"异步测试失败: {e}")
            return None

    def test_sync_import(self, nodes: List[Dict], batch_name: str) -> Dict:
        """测试同步导入"""
        logger.info(f"开始同步导入测试: {batch_name}, 节点数: {len(nodes)}")
        start_time = time.time()
        
        try:
            updater = KnowledgeGraphUpdater(self.neo4j_conn)
            success_count = 0
            failed_count = 0
            
            for node in nodes:
                try:
                    processed_node = {
                        "name": node.get("name", ""),
                        "location": node.get("location", "拉萨市"),
                        "address": node.get("address", ""),
                        "description": node.get("description", f"Description for {node.get('name', 'Unknown')}"),
                        "pub_timestamp": node.get("pub_timestamp", datetime.now().isoformat()),
                        "ranking": node.get("ranking", ""),
                        "visitor_percentage": node.get("visitor_percentage", ""),
                        "source_type": "crawler",
                        "metrics": {"ratings": 4.0}
                    }
                    
                    log_id = f"{processed_node['name']}_sync_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    weights = {"rules_valid": 1.0, "llm_valid": 0.8, "weight_valid": 0.9}
                    reason = "Sync test import"
                    
                    updater.update_knowledge_graph(processed_node, log_id, reason, weights)
                    success_count += 1
                    logger.debug(f"成功处理: {processed_node['name']}")
                    
                except Exception as e:
                    failed_count += 1
                    logger.error(f"处理失败: {node.get('name', 'Unknown')}, 错误: {e}")
            
            updater.close()
            end_time = time.time()
            duration = end_time - start_time
            
            result = {
                "batch_name": batch_name,
                "total_nodes": len(nodes),
                "success_count": success_count,
                "failed_count": failed_count,
                "duration_seconds": duration,
                "nodes_per_second": len(nodes) / duration if duration > 0 else 0,
                "type": "sync"
            }
            
            logger.info(f"同步测试完成: {success_count}/{len(nodes)} 成功, 耗时: {duration:.2f}秒")
            return result
            
        except Exception as e:
            logger.error(f"同步测试失败: {e}")
            return None

    async def run_quick_test(self):
        """运行快速测试"""
        if not self.setup_connection():
            return
            
        # 读取少量测试数据
        json_file_path = os.path.join(os.path.dirname(__file__), "data", "lhasa_knowledge_graph.json")
        
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            nodes = data.get("nodes", [])[:5]  # 只取前5个节点
            logger.info(f"读取到 {len(nodes)} 个测试节点")
        except Exception as e:
            logger.error(f"读取JSON文件失败: {e}")
            return
        
        results = []
        
        # 测试异步导入
        logger.info("\n" + "="*50)
        logger.info("测试异步导入")
        logger.info("="*50)
        
        self.neo4j_conn.clear_database()
        async_result = await self.test_async_import(nodes, "async_test_5_nodes")
        if async_result:
            results.append(async_result)
        
        # 等待一下
        await asyncio.sleep(2)
        
        # 测试同步导入
        logger.info("\n" + "="*50)
        logger.info("测试同步导入")
        logger.info("="*50)
        
        self.neo4j_conn.clear_database()
        sync_result = self.test_sync_import(nodes, "sync_test_5_nodes")
        if sync_result:
            results.append(sync_result)
        
        # 打印对比结果
        if len(results) == 2:
            logger.info("\n" + "="*60)
            logger.info("快速测试对比结果")
            logger.info("="*60)
            
            async_r = results[0]
            sync_r = results[1]
            
            logger.info(f"异步导入: {async_r['success_count']}/{async_r['total_nodes']} 成功, "
                       f"耗时: {async_r['duration_seconds']:.2f}秒, "
                       f"速度: {async_r['nodes_per_second']:.2f} 节点/秒")
            
            logger.info(f"同步导入: {sync_r['success_count']}/{sync_r['total_nodes']} 成功, "
                       f"耗时: {sync_r['duration_seconds']:.2f}秒, "
                       f"速度: {sync_r['nodes_per_second']:.2f} 节点/秒")
            
            if sync_r['duration_seconds'] > 0:
                time_improvement = ((sync_r['duration_seconds'] - async_r['duration_seconds']) / sync_r['duration_seconds']) * 100
                logger.info(f"时间提升: {time_improvement:.1f}%")
            
            if sync_r['nodes_per_second'] > 0:
                speed_improvement = ((async_r['nodes_per_second'] - sync_r['nodes_per_second']) / sync_r['nodes_per_second']) * 100
                logger.info(f"速度提升: {speed_improvement:.1f}%")
            
            logger.info("="*60)
        
        # 保存测试结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"quick_test_results_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            logger.info(f"测试结果已保存到: {filename}")
        except Exception as e:
            logger.error(f"保存测试结果失败: {e}")

async def main():
    """主函数"""
    tester = QuickTester()
    try:
        await tester.run_quick_test()
        logger.info("快速测试完成！")
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
    finally:
        if tester.neo4j_conn:
            tester.neo4j_conn.close()

if __name__ == "__main__":
    asyncio.run(main())
