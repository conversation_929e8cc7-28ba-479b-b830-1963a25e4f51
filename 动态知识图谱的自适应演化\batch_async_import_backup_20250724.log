2025-07-23 23:27:16,806 - neo4j_connection - INFO - 初始化 Neo4j 连接: bolt://localhost:7687, 用户: neo4j
2025-07-23 23:27:16,825 - neo4j_connection - INFO - Neo4j 连接验证成功
2025-07-23 23:27:16,826 - __main__ - INFO - 成功连接到Neo4j数据库
2025-07-23 23:27:16,828 - __main__ - INFO - 
============================================================
2025-07-23 23:27:16,828 - __main__ - INFO - 开始异步导入实验
2025-07-23 23:27:16,828 - __main__ - INFO - ============================================================
2025-07-23 23:27:16,828 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:27:16,828 - batch_async_import - INFO - 开始分批异步导入实验，批次大小: [10, 50, 100, 150, 200, 250]
2025-07-23 23:27:16,830 - batch_async_import - INFO - 读取到 260 个节点
2025-07-23 23:27:16,830 - batch_async_import - INFO - 去重后节点数: 258
2025-07-23 23:27:16,830 - batch_async_import - INFO - 
==================================================
2025-07-23 23:27:16,830 - batch_async_import - INFO - 开始批次实验: 10 个节点
2025-07-23 23:27:16,830 - batch_async_import - INFO - ==================================================
2025-07-23 23:27:16,900 - neo4j_connection - INFO - 清空所有节点和关系
2025-07-23 23:27:16,905 - neo4j_connection - INFO - 清空所有节点和关系
2025-07-23 23:27:16,905 - text_processor - INFO - 清空所有节点和关系
2025-07-23 23:27:16,912 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX attraction_name IF NOT EXISTS FOR (e:Attraction) ON (e.name)` has no effect.} {description: `RANGE INDEX attraction_name FOR (e:Attraction) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)'
2025-07-23 23:27:16,916 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX city_name IF NOT EXISTS FOR (e:City) ON (e.name)` has no effect.} {description: `RANGE INDEX city_name FOR (e:City) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)'
2025-07-23 23:27:16,917 - text_processor - INFO - 创建 Attraction 和 City 名称索引
2025-07-23 23:27:16,917 - batch_async_import - INFO - 数据库已清空并重置
2025-07-23 23:27:16,917 - batch_async_import - INFO - 开始异步导入批次: batch_10_nodes, 节点数: 10
2025-07-23 23:27:16,918 - batch_async_import - INFO - 处理子批次 1/4
2025-07-23 23:27:16,918 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:27:16,918 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:27:22,567 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:27:22,728 - text_processor - INFO - 成功处理实体: 布达拉宫
2025-07-23 23:27:26,183 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:27:26,202 - text_processor - INFO - 成功处理实体: 大昭寺
2025-07-23 23:27:30,806 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:27:30,830 - text_processor - INFO - 成功处理实体: 纳木措
2025-07-23 23:27:30,831 - text_processor - INFO - 过滤后景点数量: 3
2025-07-23 23:27:37,172 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:27:41,342 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:27:51,213 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:27:51,487 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:27:51,599 - batch_async_import - INFO - 处理子批次 2/4
2025-07-23 23:27:51,599 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:27:51,600 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:27:57,213 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:27:57,323 - text_processor - INFO - 成功处理实体: 色拉寺
2025-07-23 23:28:01,294 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:28:01,308 - text_processor - INFO - 成功处理实体: 八廓街
2025-07-23 23:28:13,206 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:28:20,728 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:28:20,809 - text_processor - INFO - 成功处理实体: 《文成公主》大型史诗剧
2025-07-23 23:28:20,809 - text_processor - INFO - 过滤后景点数量: 3
2025-07-23 23:28:31,841 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:28:32,770 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:28:33,909 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:28:34,116 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:28:34,226 - batch_async_import - INFO - 处理子批次 3/4
2025-07-23 23:28:34,226 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:28:34,226 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:28:39,576 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:28:39,599 - text_processor - INFO - 成功处理实体: 罗布林卡
2025-07-23 23:28:47,325 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:28:47,340 - text_processor - INFO - 成功处理实体: 药王山
2025-07-23 23:28:52,105 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:28:52,127 - text_processor - INFO - 成功处理实体: 宗角禄康公园
2025-07-23 23:28:52,128 - text_processor - INFO - 过滤后景点数量: 3
2025-07-23 23:29:04,760 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:29:09,466 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:29:12,690 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:29:12,778 - text_processor - ERROR - 创建关系失败: 药王山 -> 宗角禄康公园: Expecting value: line 1 column 1 (char 0)
2025-07-23 23:29:12,779 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:29:12,888 - batch_async_import - INFO - 处理子批次 4/4
2025-07-23 23:29:12,888 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:29:12,888 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:29:16,888 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:29:16,928 - text_processor - INFO - 成功处理实体: 哲蚌寺
2025-07-23 23:29:16,929 - text_processor - INFO - 过滤后景点数量: 1
2025-07-23 23:29:16,930 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:29:17,039 - batch_async_import - INFO - 批次 batch_10_nodes 完成: 10/10 成功, 耗时: 120.12秒
2025-07-23 23:29:17,039 - batch_async_import - INFO - 批次 10 结果:
2025-07-23 23:29:17,040 - batch_async_import - INFO -   - 成功: 10/10
2025-07-23 23:29:17,040 - batch_async_import - INFO -   - 耗时: 120.12 秒
2025-07-23 23:29:17,040 - batch_async_import - INFO -   - 速度: 0.08 节点/秒
2025-07-23 23:29:19,042 - batch_async_import - INFO - 
==================================================
2025-07-23 23:29:19,042 - batch_async_import - INFO - 开始批次实验: 50 个节点
2025-07-23 23:29:19,042 - batch_async_import - INFO - ==================================================
2025-07-23 23:29:19,062 - neo4j_connection - INFO - 清空所有节点和关系
2025-07-23 23:29:19,065 - neo4j_connection - INFO - 清空所有节点和关系
2025-07-23 23:29:19,065 - text_processor - INFO - 清空所有节点和关系
2025-07-23 23:29:19,068 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX attraction_name IF NOT EXISTS FOR (e:Attraction) ON (e.name)` has no effect.} {description: `RANGE INDEX attraction_name FOR (e:Attraction) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)'
2025-07-23 23:29:19,072 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX city_name IF NOT EXISTS FOR (e:City) ON (e.name)` has no effect.} {description: `RANGE INDEX city_name FOR (e:City) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)'
2025-07-23 23:29:19,073 - text_processor - INFO - 创建 Attraction 和 City 名称索引
2025-07-23 23:29:19,073 - batch_async_import - INFO - 数据库已清空并重置
2025-07-23 23:29:19,073 - batch_async_import - INFO - 开始异步导入批次: batch_50_nodes, 节点数: 50
2025-07-23 23:29:19,074 - batch_async_import - INFO - 处理子批次 1/17
2025-07-23 23:29:19,074 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:29:19,074 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:29:25,882 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:29:25,905 - text_processor - INFO - 成功处理实体: 布达拉宫
2025-07-23 23:29:31,748 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:29:31,845 - text_processor - INFO - 成功处理实体: 大昭寺
2025-07-23 23:29:38,918 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:29:38,941 - text_processor - INFO - 成功处理实体: 纳木措
2025-07-23 23:29:38,941 - text_processor - INFO - 过滤后景点数量: 3
2025-07-23 23:29:42,597 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:29:45,202 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:29:48,546 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:29:48,686 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:29:48,796 - batch_async_import - INFO - 处理子批次 2/17
2025-07-23 23:29:48,796 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:29:48,796 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:29:53,726 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:29:53,814 - text_processor - INFO - 成功处理实体: 色拉寺
2025-07-23 23:30:02,476 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:30:02,488 - text_processor - INFO - 成功处理实体: 八廓街
2025-07-23 23:30:08,572 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:30:20,720 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:30:20,737 - text_processor - INFO - 成功处理实体: 《文成公主》大型史诗剧
2025-07-23 23:30:20,737 - text_processor - INFO - 过滤后景点数量: 3
2025-07-23 23:30:27,278 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:30:32,520 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:30:37,893 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:30:38,000 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:30:38,104 - batch_async_import - INFO - 处理子批次 3/17
2025-07-23 23:30:38,104 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:30:38,104 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:30:42,123 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:30:42,150 - text_processor - INFO - 成功处理实体: 罗布林卡
2025-07-23 23:30:46,193 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:30:46,205 - text_processor - INFO - 成功处理实体: 药王山
2025-07-23 23:30:49,987 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:30:49,999 - text_processor - INFO - 成功处理实体: 宗角禄康公园
2025-07-23 23:30:49,999 - text_processor - INFO - 过滤后景点数量: 3
2025-07-23 23:30:53,865 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:30:55,436 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:30:57,207 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:30:57,274 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:30:57,377 - batch_async_import - INFO - 处理子批次 4/17
2025-07-23 23:30:57,377 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:30:57,378 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:31:10,609 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:31:10,623 - text_processor - INFO - 成功处理实体: 哲蚌寺
2025-07-23 23:31:17,359 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:31:17,373 - text_processor - INFO - 成功处理实体: 小昭寺
2025-07-23 23:31:21,760 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:31:21,820 - text_processor - INFO - 成功处理实体: 西藏博物馆
2025-07-23 23:31:21,820 - text_processor - INFO - 过滤后景点数量: 3
2025-07-23 23:33:23,040 - neo4j_connection - INFO - 初始化 Neo4j 连接: bolt://localhost:7687, 用户: neo4j
2025-07-23 23:33:23,048 - neo4j_connection - INFO - Neo4j 连接验证成功
2025-07-23 23:33:23,049 - __main__ - INFO - 成功连接到Neo4j数据库
2025-07-23 23:33:23,049 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:33:23,049 - __main__ - INFO - 开始分批异步导入实验，批次大小: [10, 50, 100, 150, 200, 250]
2025-07-23 23:33:23,050 - __main__ - INFO - 读取到 260 个节点
2025-07-23 23:33:23,050 - __main__ - INFO - 去重后节点数: 258
2025-07-23 23:33:23,051 - __main__ - INFO - 
==================================================
2025-07-23 23:33:23,051 - __main__ - INFO - 开始批次实验: 10 个节点
2025-07-23 23:33:23,051 - __main__ - INFO - ==================================================
2025-07-23 23:33:23,053 - neo4j_connection - INFO - 清空所有节点和关系
2025-07-23 23:33:23,054 - neo4j_connection - INFO - 清空所有节点和关系
2025-07-23 23:33:23,054 - text_processor - INFO - 清空所有节点和关系
2025-07-23 23:33:23,057 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX attraction_name IF NOT EXISTS FOR (e:Attraction) ON (e.name)` has no effect.} {description: `RANGE INDEX attraction_name FOR (e:Attraction) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)'
2025-07-23 23:33:23,059 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX city_name IF NOT EXISTS FOR (e:City) ON (e.name)` has no effect.} {description: `RANGE INDEX city_name FOR (e:City) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)'
2025-07-23 23:33:23,060 - text_processor - INFO - 创建 Attraction 和 City 名称索引
2025-07-23 23:33:23,060 - __main__ - INFO - 数据库已清空并重置
2025-07-23 23:33:23,060 - __main__ - INFO - 开始异步导入批次: batch_10_nodes, 节点数: 10
2025-07-23 23:33:23,060 - __main__ - INFO - 处理子批次 1/4
2025-07-23 23:33:23,060 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:33:23,060 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:33:30,502 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:33:30,581 - text_processor - INFO - 成功处理实体: 布达拉宫
2025-07-23 23:33:37,207 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:33:37,237 - text_processor - INFO - 成功处理实体: 大昭寺
2025-07-23 23:33:42,289 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:33:42,361 - text_processor - INFO - 成功处理实体: 纳木措
2025-07-23 23:33:42,361 - text_processor - INFO - 过滤后景点数量: 3
2025-07-23 23:33:48,725 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:33:56,170 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:34:02,643 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:34:02,754 - neo4j_crud - ERROR - Cypher 语法错误: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input 'RELATION': expected a parameter, '&', '*', ':', 'WHERE', ']', '{' or '|' (line 4, column 34 (offset: 152))

"            MERGE (source)-[r:NO RELATION]->(target)"

                                  ^}, 查询: 
            MATCH (source:Attraction {name: $source_name})
            MATCH (target:Attraction {name: $target_name})
            MERGE (source)-[r:NO RELATION]->(target)
            SET r += $props
            RETURN r
            , 参数: 布达拉宫 -> 纳木措
2025-07-23 23:34:02,764 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:34:02,869 - __main__ - INFO - 处理子批次 2/4
2025-07-23 23:34:02,869 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:34:02,869 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:34:06,527 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:34:06,571 - text_processor - INFO - 成功处理实体: 色拉寺
2025-07-23 23:34:11,262 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:34:11,275 - text_processor - INFO - 成功处理实体: 八廓街
2025-07-23 23:34:20,118 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:34:28,727 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:34:28,749 - text_processor - INFO - 成功处理实体: 《文成公主》大型史诗剧
2025-07-23 23:34:28,750 - text_processor - INFO - 过滤后景点数量: 3
2025-07-23 23:34:32,848 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:34:33,231 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:34:36,731 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:34:36,771 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:34:36,885 - __main__ - INFO - 处理子批次 3/4
2025-07-23 23:34:36,886 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:34:36,887 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:34:48,289 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:34:48,347 - text_processor - INFO - 成功处理实体: 罗布林卡
2025-07-23 23:34:53,748 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:34:53,761 - text_processor - INFO - 成功处理实体: 药王山
2025-07-23 23:34:58,810 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:34:58,830 - text_processor - INFO - 成功处理实体: 宗角禄康公园
2025-07-23 23:34:58,831 - text_processor - INFO - 过滤后景点数量: 3
2025-07-23 23:35:07,586 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:35:11,969 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:35:12,592 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:35:12,747 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:35:12,858 - __main__ - INFO - 处理子批次 4/4
2025-07-23 23:35:12,858 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:35:12,858 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:35:17,114 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:35:17,145 - text_processor - INFO - 成功处理实体: 哲蚌寺
2025-07-23 23:35:17,146 - text_processor - INFO - 过滤后景点数量: 1
2025-07-23 23:35:17,146 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:35:17,256 - __main__ - INFO - 批次 batch_10_nodes 完成: 10/10 成功, 耗时: 114.20秒
2025-07-23 23:35:17,257 - __main__ - INFO - 批次 10 结果:
2025-07-23 23:35:17,257 - __main__ - INFO -   - 成功: 10/10
2025-07-23 23:35:17,257 - __main__ - INFO -   - 耗时: 114.20 秒
2025-07-23 23:35:17,257 - __main__ - INFO -   - 速度: 0.09 节点/秒
2025-07-23 23:35:19,263 - __main__ - INFO - 
==================================================
2025-07-23 23:35:19,263 - __main__ - INFO - 开始批次实验: 50 个节点
2025-07-23 23:35:19,264 - __main__ - INFO - ==================================================
2025-07-23 23:35:19,274 - neo4j_connection - INFO - 清空所有节点和关系
2025-07-23 23:35:19,276 - neo4j_connection - INFO - 清空所有节点和关系
2025-07-23 23:35:19,276 - text_processor - INFO - 清空所有节点和关系
2025-07-23 23:35:19,278 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX attraction_name IF NOT EXISTS FOR (e:Attraction) ON (e.name)` has no effect.} {description: `RANGE INDEX attraction_name FOR (e:Attraction) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)'
2025-07-23 23:35:19,281 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX city_name IF NOT EXISTS FOR (e:City) ON (e.name)` has no effect.} {description: `RANGE INDEX city_name FOR (e:City) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)'
2025-07-23 23:35:19,282 - text_processor - INFO - 创建 Attraction 和 City 名称索引
2025-07-23 23:35:19,282 - __main__ - INFO - 数据库已清空并重置
2025-07-23 23:35:19,282 - __main__ - INFO - 开始异步导入批次: batch_50_nodes, 节点数: 50
2025-07-23 23:35:19,282 - __main__ - INFO - 处理子批次 1/17
2025-07-23 23:35:19,283 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:35:19,283 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:35:24,800 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:35:24,814 - text_processor - INFO - 成功处理实体: 布达拉宫
2025-07-23 23:35:30,894 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:35:30,964 - text_processor - INFO - 成功处理实体: 大昭寺
2025-07-23 23:35:37,709 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:35:37,724 - text_processor - INFO - 成功处理实体: 纳木措
2025-07-23 23:35:37,724 - text_processor - INFO - 过滤后景点数量: 3
2025-07-23 23:35:44,726 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:35:52,886 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:35:59,394 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:35:59,457 - neo4j_crud - ERROR - Cypher 语法错误: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input 'Clear': expected a parameter, '&', '*', ':', 'WHERE', ']', '{' or '|' (line 4, column 34 (offset: 152))

"            MERGE (source)-[r:NO Clear Relationship]->(target)"

                                  ^}, 查询: 
            MATCH (source:Attraction {name: $source_name})
            MATCH (target:Attraction {name: $target_name})
            MERGE (source)-[r:NO Clear Relationship]->(target)
            SET r += $props
            RETURN r
            , 参数: 布达拉宫 -> 纳木措
2025-07-23 23:35:59,466 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:35:59,576 - __main__ - INFO - 处理子批次 2/17
2025-07-23 23:35:59,576 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:35:59,577 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:36:06,954 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:36:07,035 - text_processor - INFO - 成功处理实体: 色拉寺
2025-07-23 23:36:21,091 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:36:21,111 - text_processor - INFO - 成功处理实体: 八廓街
2025-07-23 23:36:28,097 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:36:39,372 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:36:39,387 - text_processor - INFO - 成功处理实体: 《文成公主》大型史诗剧
2025-07-23 23:36:39,388 - text_processor - INFO - 过滤后景点数量: 3
2025-07-23 23:36:43,263 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:36:45,271 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:36:45,442 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:36:45,492 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:36:45,606 - __main__ - INFO - 处理子批次 3/17
2025-07-23 23:36:45,606 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:36:45,606 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:36:52,767 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:36:52,794 - text_processor - INFO - 成功处理实体: 罗布林卡
2025-07-23 23:36:58,102 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:36:58,117 - text_processor - INFO - 成功处理实体: 药王山
2025-07-23 23:37:03,486 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:37:03,513 - text_processor - INFO - 成功处理实体: 宗角禄康公园
2025-07-23 23:37:03,514 - text_processor - INFO - 过滤后景点数量: 3
2025-07-23 23:37:10,810 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:37:14,751 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:37:15,895 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:37:16,010 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:37:16,120 - __main__ - INFO - 处理子批次 4/17
2025-07-23 23:37:16,120 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:37:16,120 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:37:20,090 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:37:20,132 - text_processor - INFO - 成功处理实体: 哲蚌寺
2025-07-23 23:37:25,048 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:37:25,070 - text_processor - INFO - 成功处理实体: 小昭寺
2025-07-23 23:37:31,370 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:37:31,414 - text_processor - INFO - 成功处理实体: 西藏博物馆
2025-07-23 23:37:31,415 - text_processor - INFO - 过滤后景点数量: 3
2025-07-23 23:37:37,569 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:37:38,939 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:37:38,958 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:37:38,961 - text_processor - ERROR - 创建关系失败: 哲蚌寺 -> 小昭寺: Expecting value: line 1 column 1 (char 0)
2025-07-23 23:37:39,031 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:37:39,139 - __main__ - INFO - 处理子批次 5/17
2025-07-23 23:37:39,139 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:37:39,140 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:37:44,940 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:37:59,389 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:37:59,425 - text_processor - INFO - 成功处理实体: 娘热民俗风情园
2025-07-23 23:38:04,496 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:38:04,521 - text_processor - INFO - 成功处理实体: 仓姑寺
2025-07-23 23:38:11,971 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:38:11,993 - text_processor - INFO - 成功处理实体: 扎基寺
2025-07-23 23:38:11,993 - text_processor - INFO - 过滤后景点数量: 2
2025-07-23 23:38:25,744 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:38:25,760 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:38:25,869 - __main__ - INFO - 处理子批次 6/17
2025-07-23 23:38:25,870 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:38:25,870 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:38:31,127 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:38:31,167 - text_processor - INFO - 成功处理实体: 西藏拉萨清真大寺
2025-07-23 23:38:39,731 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:38:39,753 - text_processor - INFO - 成功处理实体: 拉萨河
2025-07-23 23:38:44,136 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:38:44,163 - text_processor - INFO - 成功处理实体: 帕邦喀
2025-07-23 23:38:44,163 - text_processor - INFO - 过滤后景点数量: 3
2025-07-23 23:38:49,125 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:38:51,180 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:38:58,614 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:38:58,699 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:38:58,804 - __main__ - INFO - 处理子批次 7/17
2025-07-23 23:38:58,804 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:38:58,804 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:39:08,144 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:39:08,230 - text_processor - INFO - 成功处理实体: 拉鲁湿地国家级自然保护区
2025-07-23 23:39:15,242 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:39:15,262 - text_processor - INFO - 成功处理实体: 喜德寺
2025-07-23 23:39:22,169 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:39:22,181 - text_processor - INFO - 成功处理实体: 羊八井
2025-07-23 23:39:22,182 - text_processor - INFO - 过滤后景点数量: 2
2025-07-23 23:39:35,012 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:39:35,106 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:39:35,213 - __main__ - INFO - 处理子批次 8/17
2025-07-23 23:39:35,213 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:39:35,213 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:39:40,158 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:39:51,885 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:39:51,915 - text_processor - INFO - 成功处理实体: 直贡噶举派寺庙群
2025-07-23 23:39:57,398 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:39:57,424 - text_processor - INFO - 成功处理实体: 甘丹寺
2025-07-23 23:40:04,762 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:40:04,784 - text_processor - INFO - 成功处理实体: 米拉山口
2025-07-23 23:40:04,785 - text_processor - INFO - 过滤后景点数量: 3
2025-07-23 23:40:11,204 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:40:19,213 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:40:21,734 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:40:21,813 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:40:21,926 - __main__ - INFO - 处理子批次 9/17
2025-07-23 23:40:21,926 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:40:21,926 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:40:26,931 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:40:26,969 - text_processor - INFO - 成功处理实体: 和平解放纪念碑
2025-07-23 23:40:32,990 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:40:33,012 - text_processor - INFO - 成功处理实体: 布达拉宫广场
2025-07-23 23:40:39,984 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:40:39,996 - text_processor - INFO - 成功处理实体: 那根拉山口
2025-07-23 23:40:39,996 - text_processor - INFO - 过滤后景点数量: 3
2025-07-23 23:40:44,365 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:40:46,323 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:40:55,880 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:40:55,909 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:40:56,026 - __main__ - INFO - 处理子批次 10/17
2025-07-23 23:40:56,026 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:40:56,026 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:41:02,808 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:41:02,857 - text_processor - INFO - 成功处理实体: 木如寺印经院
2025-07-23 23:41:06,205 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:41:06,220 - text_processor - INFO - 成功处理实体: 下密寺
2025-07-23 23:41:11,144 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:41:11,170 - text_processor - INFO - 成功处理实体: 驻藏大臣衙门遗址
2025-07-23 23:41:11,171 - text_processor - INFO - 过滤后景点数量: 3
2025-07-23 23:41:15,809 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:41:17,122 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:41:20,566 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:41:20,611 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:41:20,723 - __main__ - INFO - 处理子批次 11/17
2025-07-23 23:41:20,723 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:41:20,724 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:41:27,969 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:41:28,016 - text_processor - INFO - 成功处理实体: 小昭寺路
2025-07-23 23:41:42,822 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:41:50,499 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:41:50,533 - text_processor - INFO - 成功处理实体: 拉萨民族文化艺术宫
2025-07-23 23:41:55,667 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:42:00,787 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:42:00,804 - text_processor - INFO - 成功处理实体: 5238
2025-07-23 23:42:00,804 - text_processor - INFO - 过滤后景点数量: 0
2025-07-23 23:42:00,804 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:42:00,919 - __main__ - INFO - 处理子批次 12/17
2025-07-23 23:42:00,919 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:42:00,919 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:42:07,698 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:42:24,429 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:42:24,453 - text_processor - INFO - 成功处理实体: 纳木措扎西岛
2025-07-23 23:42:36,075 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:42:53,368 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:42:53,387 - text_processor - INFO - 成功处理实体: 西藏藏医药文化博览中心
2025-07-23 23:42:56,861 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:42:56,906 - text_processor - INFO - 成功处理实体: 达扎路恭纪功碑
2025-07-23 23:42:56,907 - text_processor - INFO - 过滤后景点数量: 1
2025-07-23 23:42:56,907 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:42:57,012 - __main__ - INFO - 处理子批次 13/17
2025-07-23 23:42:57,014 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:42:57,015 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:43:08,023 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:43:15,687 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:43:15,705 - text_processor - INFO - 成功处理实体: 甲玛王宫
2025-07-23 23:43:21,332 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:43:21,347 - text_processor - INFO - 成功处理实体: 扎希寺
2025-07-23 23:43:25,045 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:43:25,067 - text_processor - INFO - 成功处理实体: 楚布寺
2025-07-23 23:43:25,069 - text_processor - INFO - 过滤后景点数量: 0
2025-07-23 23:43:25,070 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:43:25,177 - __main__ - INFO - 处理子批次 14/17
2025-07-23 23:43:25,177 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:43:25,178 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:43:31,880 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:43:31,924 - text_processor - INFO - 成功处理实体: 松赞干布出生地
2025-07-23 23:43:37,053 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:43:37,068 - text_processor - INFO - 成功处理实体: 聂当度母殿和聂当大佛
2025-07-23 23:43:43,738 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:43:43,756 - text_processor - INFO - 成功处理实体: 策门林寺
2025-07-23 23:43:43,756 - text_processor - INFO - 过滤后景点数量: 1
2025-07-23 23:43:43,757 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:43:43,867 - __main__ - INFO - 处理子批次 15/17
2025-07-23 23:43:43,868 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:43:43,868 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:43:53,097 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:43:53,151 - text_processor - INFO - 成功处理实体: 鲁普岩寺
2025-07-23 23:43:58,037 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:43:58,054 - text_processor - INFO - 成功处理实体: 仙足岛
2025-07-23 23:44:06,830 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:44:06,856 - text_processor - INFO - 成功处理实体: 曲贡古遗址
2025-07-23 23:44:06,857 - text_processor - INFO - 过滤后景点数量: 1
2025-07-23 23:44:06,858 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:44:06,968 - __main__ - INFO - 处理子批次 16/17
2025-07-23 23:44:06,969 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:44:06,969 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:44:14,141 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:44:14,179 - text_processor - INFO - 成功处理实体: 乃琼寺
2025-07-23 23:44:19,517 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:44:19,530 - text_processor - INFO - 成功处理实体: 次巴拉康寺
2025-07-23 23:44:28,070 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:44:28,086 - text_processor - INFO - 成功处理实体: 清政府驻藏大臣衙门旧址
2025-07-23 23:44:28,086 - text_processor - INFO - 过滤后景点数量: 1
2025-07-23 23:44:28,087 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:44:28,198 - __main__ - INFO - 处理子批次 17/17
2025-07-23 23:44:28,198 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:44:28,198 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:44:34,086 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:44:40,937 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:44:40,964 - text_processor - INFO - 成功处理实体: 热振寺
2025-07-23 23:44:44,827 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:44:44,841 - text_processor - INFO - 成功处理实体: 关帝庙
2025-07-23 23:44:44,842 - text_processor - INFO - 过滤后景点数量: 0
2025-07-23 23:44:44,842 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:44:44,956 - __main__ - INFO - 批次 batch_50_nodes 完成: 50/50 成功, 耗时: 565.67秒
2025-07-23 23:44:44,956 - __main__ - INFO - 批次 50 结果:
2025-07-23 23:44:44,957 - __main__ - INFO -   - 成功: 50/50
2025-07-23 23:44:44,957 - __main__ - INFO -   - 耗时: 565.67 秒
2025-07-23 23:44:44,957 - __main__ - INFO -   - 速度: 0.09 节点/秒
2025-07-23 23:44:46,960 - __main__ - INFO - 
==================================================
2025-07-23 23:44:46,960 - __main__ - INFO - 开始批次实验: 100 个节点
2025-07-23 23:44:46,960 - __main__ - INFO - ==================================================
2025-07-23 23:44:46,982 - neo4j_connection - INFO - 清空所有节点和关系
2025-07-23 23:44:46,985 - neo4j_connection - INFO - 清空所有节点和关系
2025-07-23 23:44:46,986 - text_processor - INFO - 清空所有节点和关系
2025-07-23 23:44:46,989 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX attraction_name IF NOT EXISTS FOR (e:Attraction) ON (e.name)` has no effect.} {description: `RANGE INDEX attraction_name FOR (e:Attraction) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)'
2025-07-23 23:44:46,992 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX city_name IF NOT EXISTS FOR (e:City) ON (e.name)` has no effect.} {description: `RANGE INDEX city_name FOR (e:City) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)'
2025-07-23 23:44:46,993 - text_processor - INFO - 创建 Attraction 和 City 名称索引
2025-07-23 23:44:46,993 - __main__ - INFO - 数据库已清空并重置
2025-07-23 23:44:46,993 - __main__ - INFO - 开始异步导入批次: batch_100_nodes, 节点数: 100
2025-07-23 23:44:46,993 - __main__ - INFO - 处理子批次 1/34
2025-07-23 23:44:46,995 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:44:46,995 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:44:51,987 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:44:52,128 - text_processor - INFO - 成功处理实体: 布达拉宫
2025-07-23 23:44:57,957 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:44:57,978 - text_processor - INFO - 成功处理实体: 大昭寺
2025-07-23 23:45:04,034 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:45:04,115 - text_processor - INFO - 成功处理实体: 纳木措
2025-07-23 23:45:04,115 - text_processor - INFO - 过滤后景点数量: 3
2025-07-23 23:45:14,178 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:45:24,354 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:45:27,216 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:45:27,514 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:45:27,620 - __main__ - INFO - 处理子批次 2/34
2025-07-23 23:45:27,620 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:45:27,621 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:45:30,519 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:45:30,547 - text_processor - INFO - 成功处理实体: 色拉寺
2025-07-23 23:45:34,845 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:45:34,861 - text_processor - INFO - 成功处理实体: 八廓街
2025-07-23 23:45:38,745 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:45:45,431 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:45:45,495 - text_processor - INFO - 成功处理实体: 《文成公主》大型史诗剧
2025-07-23 23:45:45,495 - text_processor - INFO - 过滤后景点数量: 3
2025-07-23 23:45:55,890 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:46:01,287 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:46:02,542 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:46:02,593 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:46:02,701 - __main__ - INFO - 处理子批次 3/34
2025-07-23 23:46:02,701 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:46:02,702 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:46:11,247 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:46:11,283 - text_processor - INFO - 成功处理实体: 罗布林卡
2025-07-23 23:46:21,967 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:46:21,990 - text_processor - INFO - 成功处理实体: 药王山
2025-07-23 23:46:32,538 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:46:32,583 - text_processor - INFO - 成功处理实体: 宗角禄康公园
2025-07-23 23:46:32,584 - text_processor - INFO - 过滤后景点数量: 3
2025-07-23 23:46:43,950 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:46:45,615 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:46:48,260 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:46:48,325 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:46:48,430 - __main__ - INFO - 处理子批次 4/34
2025-07-23 23:46:48,430 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:46:48,430 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:46:57,708 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:46:57,748 - text_processor - INFO - 成功处理实体: 哲蚌寺
2025-07-23 23:47:03,238 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:47:03,265 - text_processor - INFO - 成功处理实体: 小昭寺
2025-07-23 23:47:11,653 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:47:11,703 - text_processor - INFO - 成功处理实体: 西藏博物馆
2025-07-23 23:47:11,705 - text_processor - INFO - 过滤后景点数量: 3
2025-07-23 23:47:16,977 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:47:18,221 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:47:19,007 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:47:19,138 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:47:19,241 - __main__ - INFO - 处理子批次 5/34
2025-07-23 23:47:19,241 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:47:19,242 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:47:24,540 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:47:30,420 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:47:30,441 - text_processor - INFO - 成功处理实体: 娘热民俗风情园
2025-07-23 23:47:37,872 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:47:37,884 - text_processor - INFO - 成功处理实体: 仓姑寺
2025-07-23 23:47:44,122 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:47:44,145 - text_processor - INFO - 成功处理实体: 扎基寺
2025-07-23 23:47:44,145 - text_processor - INFO - 过滤后景点数量: 2
2025-07-23 23:47:53,031 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:47:53,052 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:47:53,167 - __main__ - INFO - 处理子批次 6/34
2025-07-23 23:47:53,167 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:47:53,167 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:47:57,954 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:47:57,968 - text_processor - INFO - 成功处理实体: 西藏拉萨清真大寺
2025-07-23 23:48:06,049 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:48:06,062 - text_processor - INFO - 成功处理实体: 拉萨河
2025-07-23 23:48:10,804 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:48:10,839 - text_processor - INFO - 成功处理实体: 帕邦喀
2025-07-23 23:48:10,840 - text_processor - INFO - 过滤后景点数量: 3
2025-07-23 23:48:16,671 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:48:19,733 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:48:20,865 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:48:20,891 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:48:20,995 - __main__ - INFO - 处理子批次 7/34
2025-07-23 23:48:20,996 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:48:20,997 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:48:28,008 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:48:28,065 - text_processor - INFO - 成功处理实体: 拉鲁湿地国家级自然保护区
2025-07-23 23:48:34,227 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:48:34,249 - text_processor - INFO - 成功处理实体: 喜德寺
2025-07-23 23:48:39,048 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:48:39,061 - text_processor - INFO - 成功处理实体: 羊八井
2025-07-23 23:48:39,061 - text_processor - INFO - 过滤后景点数量: 2
2025-07-23 23:48:56,892 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:48:56,896 - text_processor - ERROR - 创建关系失败: 拉鲁湿地国家级自然保护区 -> 羊八井: Extra data: line 23 column 1 (char 531)
2025-07-23 23:48:56,896 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:48:57,009 - __main__ - INFO - 处理子批次 8/34
2025-07-23 23:48:57,009 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:48:57,009 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:49:10,606 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:49:21,062 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:49:21,100 - text_processor - INFO - 成功处理实体: 直贡噶举派寺庙群
2025-07-23 23:49:32,701 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:49:32,716 - text_processor - INFO - 成功处理实体: 甘丹寺
2025-07-23 23:49:36,664 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:49:36,677 - text_processor - INFO - 成功处理实体: 米拉山口
2025-07-23 23:49:36,677 - text_processor - INFO - 过滤后景点数量: 3
2025-07-23 23:49:40,608 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:49:42,887 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:49:43,513 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:49:43,577 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:49:43,682 - __main__ - INFO - 处理子批次 9/34
2025-07-23 23:49:43,683 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:49:43,684 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:49:56,018 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:49:56,037 - text_processor - INFO - 成功处理实体: 和平解放纪念碑
2025-07-23 23:50:00,927 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:50:00,951 - text_processor - INFO - 成功处理实体: 布达拉宫广场
2025-07-23 23:50:07,554 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:50:07,567 - text_processor - INFO - 成功处理实体: 那根拉山口
2025-07-23 23:50:07,568 - text_processor - INFO - 过滤后景点数量: 3
2025-07-23 23:50:13,860 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:50:21,678 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:50:22,409 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:50:22,494 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:50:22,607 - __main__ - INFO - 处理子批次 10/34
2025-07-23 23:50:22,607 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:50:22,608 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:50:27,544 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:50:27,582 - text_processor - INFO - 成功处理实体: 木如寺印经院
2025-07-23 23:50:32,916 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:50:32,930 - text_processor - INFO - 成功处理实体: 下密寺
2025-07-23 23:50:36,608 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:50:36,621 - text_processor - INFO - 成功处理实体: 驻藏大臣衙门遗址
2025-07-23 23:50:36,622 - text_processor - INFO - 过滤后景点数量: 3
2025-07-23 23:50:43,346 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:50:46,313 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:50:48,065 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:50:48,111 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:50:48,227 - __main__ - INFO - 处理子批次 11/34
2025-07-23 23:50:48,227 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:50:48,227 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:50:53,392 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:50:53,432 - text_processor - INFO - 成功处理实体: 小昭寺路
2025-07-23 23:50:58,692 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:51:11,226 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:51:11,240 - text_processor - INFO - 成功处理实体: 拉萨民族文化艺术宫
2025-07-23 23:51:18,440 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:51:28,357 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:51:28,372 - text_processor - INFO - 成功处理实体: 5238
2025-07-23 23:51:28,373 - text_processor - INFO - 过滤后景点数量: 0
2025-07-23 23:51:28,373 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:51:28,485 - __main__ - INFO - 处理子批次 12/34
2025-07-23 23:51:28,485 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:51:28,486 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:51:35,170 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:51:50,246 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:51:50,280 - text_processor - INFO - 成功处理实体: 纳木措扎西岛
2025-07-23 23:51:59,261 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:52:06,682 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:52:06,707 - text_processor - INFO - 成功处理实体: 西藏藏医药文化博览中心
2025-07-23 23:52:09,959 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:52:09,972 - text_processor - INFO - 成功处理实体: 达扎路恭纪功碑
2025-07-23 23:52:09,972 - text_processor - INFO - 过滤后景点数量: 1
2025-07-23 23:52:09,972 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:52:10,084 - __main__ - INFO - 处理子批次 13/34
2025-07-23 23:52:10,084 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:52:10,084 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:52:13,918 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:52:20,646 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:52:20,714 - text_processor - INFO - 成功处理实体: 甲玛王宫
2025-07-23 23:52:34,694 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:52:34,708 - text_processor - INFO - 成功处理实体: 扎希寺
2025-07-23 23:52:37,450 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:52:37,463 - text_processor - INFO - 成功处理实体: 楚布寺
2025-07-23 23:52:37,463 - text_processor - INFO - 过滤后景点数量: 0
2025-07-23 23:52:37,464 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:52:37,572 - __main__ - INFO - 处理子批次 14/34
2025-07-23 23:52:37,572 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:52:37,573 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:52:41,942 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:52:41,982 - text_processor - INFO - 成功处理实体: 松赞干布出生地
2025-07-23 23:52:47,791 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:52:47,814 - text_processor - INFO - 成功处理实体: 聂当度母殿和聂当大佛
2025-07-23 23:52:55,139 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:52:55,155 - text_processor - INFO - 成功处理实体: 策门林寺
2025-07-23 23:52:55,155 - text_processor - INFO - 过滤后景点数量: 1
2025-07-23 23:52:55,156 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:52:55,267 - __main__ - INFO - 处理子批次 15/34
2025-07-23 23:52:55,267 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:52:55,267 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:53:01,621 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:53:01,639 - text_processor - INFO - 成功处理实体: 鲁普岩寺
2025-07-23 23:53:06,804 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:53:06,827 - text_processor - INFO - 成功处理实体: 仙足岛
2025-07-23 23:53:14,701 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:53:14,724 - text_processor - INFO - 成功处理实体: 曲贡古遗址
2025-07-23 23:53:14,725 - text_processor - INFO - 过滤后景点数量: 1
2025-07-23 23:53:14,725 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:53:14,827 - __main__ - INFO - 处理子批次 16/34
2025-07-23 23:53:14,827 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:53:14,828 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:53:20,472 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:53:20,518 - text_processor - INFO - 成功处理实体: 乃琼寺
2025-07-23 23:53:24,359 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:53:24,372 - text_processor - INFO - 成功处理实体: 次巴拉康寺
2025-07-23 23:53:30,060 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:53:30,082 - text_processor - INFO - 成功处理实体: 清政府驻藏大臣衙门旧址
2025-07-23 23:53:30,082 - text_processor - INFO - 过滤后景点数量: 1
2025-07-23 23:53:30,083 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:53:30,198 - __main__ - INFO - 处理子批次 17/34
2025-07-23 23:53:30,198 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:53:30,199 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:53:37,533 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:53:41,948 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:53:41,963 - text_processor - INFO - 成功处理实体: 热振寺
2025-07-23 23:53:45,461 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:53:45,478 - text_processor - INFO - 成功处理实体: 关帝庙
2025-07-23 23:53:55,959 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:54:07,779 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:54:07,792 - text_processor - INFO - 成功处理实体: 喜德林
2025-07-23 23:54:07,792 - text_processor - INFO - 过滤后景点数量: 0
2025-07-23 23:54:07,793 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:54:07,905 - __main__ - INFO - 处理子批次 18/34
2025-07-23 23:54:07,905 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:54:07,905 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:54:14,594 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:54:20,531 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:54:20,565 - text_processor - INFO - 成功处理实体: 白色寺
2025-07-23 23:54:29,574 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:54:35,174 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:54:35,191 - text_processor - INFO - 成功处理实体: 西藏非遗博物馆附属建筑1栋
2025-07-23 23:54:45,894 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:54:52,862 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:54:52,878 - text_processor - INFO - 成功处理实体: 纳木寺
2025-07-23 23:54:52,878 - text_processor - INFO - 过滤后景点数量: 0
2025-07-23 23:54:52,879 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:54:52,984 - __main__ - INFO - 处理子批次 19/34
2025-07-23 23:54:52,984 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:54:52,984 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:54:59,099 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:55:06,024 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:55:06,076 - text_processor - INFO - 成功处理实体: 林周县楚杰寺
2025-07-23 23:55:14,131 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:55:29,730 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:55:29,762 - text_processor - INFO - 成功处理实体: 中华文化园露营地
2025-07-23 23:55:38,025 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:55:51,176 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:55:51,204 - text_processor - INFO - 成功处理实体: 秀色才纳净土博览园
2025-07-23 23:55:51,204 - text_processor - INFO - 过滤后景点数量: 0
2025-07-23 23:55:51,204 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:55:51,309 - __main__ - INFO - 处理子批次 20/34
2025-07-23 23:55:51,309 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:55:51,311 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:55:59,871 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:56:04,204 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:56:04,276 - text_processor - INFO - 成功处理实体: 茶马古道马帮落脚点遗址
2025-07-23 23:56:17,099 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:56:23,555 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:56:23,576 - text_processor - INFO - 成功处理实体: 山野星空露营-吉隆营地
2025-07-23 23:56:29,801 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:56:40,059 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:56:40,084 - text_processor - INFO - 成功处理实体: 奇秀园林
2025-07-23 23:56:40,085 - text_processor - INFO - 过滤后景点数量: 0
2025-07-23 23:56:40,086 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:56:40,199 - __main__ - INFO - 处理子批次 21/34
2025-07-23 23:56:40,199 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:56:40,199 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:56:41,660 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:56:53,170 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:56:53,188 - text_processor - INFO - 成功处理实体: 洛堆吉曲康桑区
2025-07-23 23:57:02,336 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:57:15,787 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:57:15,809 - text_processor - INFO - 成功处理实体: 格桑林卡
2025-07-23 23:57:23,655 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:57:31,682 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:57:31,698 - text_processor - INFO - 成功处理实体: 塔玉贡康寺
2025-07-23 23:57:31,699 - text_processor - INFO - 过滤后景点数量: 0
2025-07-23 23:57:31,699 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:57:31,808 - __main__ - INFO - 处理子批次 22/34
2025-07-23 23:57:31,808 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:57:31,808 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:57:38,888 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:57:44,846 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:57:44,865 - text_processor - INFO - 成功处理实体: 吉布寺
2025-07-23 23:57:50,327 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:57:57,040 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:57:57,055 - text_processor - INFO - 成功处理实体: 美珠杂日生态乐园
2025-07-23 23:58:09,394 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:58:14,435 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:58:14,454 - text_processor - INFO - 成功处理实体: 舒适的露营地
2025-07-23 23:58:14,454 - text_processor - INFO - 过滤后景点数量: 0
2025-07-23 23:58:14,455 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:58:14,558 - __main__ - INFO - 处理子批次 23/34
2025-07-23 23:58:14,559 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:58:14,559 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:58:21,899 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:58:29,962 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:58:30,000 - text_processor - INFO - 成功处理实体: 钱学森雕像
2025-07-23 23:58:40,296 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:58:46,226 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:58:46,251 - text_processor - INFO - 成功处理实体: 嘎卓卓休闲露营基地
2025-07-23 23:58:52,007 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:58:57,182 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:58:57,199 - text_processor - INFO - 成功处理实体: 明珠林
2025-07-23 23:58:57,199 - text_processor - INFO - 过滤后景点数量: 0
2025-07-23 23:58:57,199 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:58:57,302 - __main__ - INFO - 处理子批次 24/34
2025-07-23 23:58:57,302 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:58:57,303 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:59:03,201 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:59:18,220 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:59:18,264 - text_processor - INFO - 成功处理实体: 西藏林周草牧业科技小院
2025-07-23 23:59:22,526 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:59:29,210 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:59:29,230 - text_processor - INFO - 成功处理实体: 内马厩
2025-07-23 23:59:34,471 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:59:41,392 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-23 23:59:41,406 - text_processor - INFO - 成功处理实体: 尤隆布
2025-07-23 23:59:41,406 - text_processor - INFO - 过滤后景点数量: 0
2025-07-23 23:59:41,406 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-23 23:59:41,518 - __main__ - INFO - 处理子批次 25/34
2025-07-23 23:59:41,518 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-23 23:59:41,519 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-23 23:59:50,686 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:00:03,591 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:00:03,606 - text_processor - INFO - 成功处理实体: 龙日秋摩
2025-07-24 00:00:08,576 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:00:15,817 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:00:15,833 - text_processor - INFO - 成功处理实体: 洛巴机康
2025-07-24 00:00:20,756 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:00:32,739 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:00:32,753 - text_processor - INFO - 成功处理实体: 崩布朗
2025-07-24 00:00:32,754 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:00:32,754 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:00:32,856 - __main__ - INFO - 处理子批次 26/34
2025-07-24 00:00:32,857 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:00:32,857 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:00:44,806 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:00:53,034 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:00:53,067 - text_processor - INFO - 成功处理实体: 加错
2025-07-24 00:00:57,618 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:01:04,475 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:01:04,498 - text_processor - INFO - 成功处理实体: 达尔多拉
2025-07-24 00:01:11,928 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:01:20,147 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:01:20,160 - text_processor - INFO - 成功处理实体: 林周县杰堆寺
2025-07-24 00:01:20,160 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:01:20,161 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:01:20,273 - __main__ - INFO - 处理子批次 27/34
2025-07-24 00:01:20,273 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:01:20,274 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:01:25,230 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:01:34,592 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:01:34,640 - text_processor - INFO - 成功处理实体: 扎日阿布塘
2025-07-24 00:01:39,710 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:01:44,644 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:01:44,659 - text_processor - INFO - 成功处理实体: 城关区净土国家级旅游景区
2025-07-24 00:01:54,885 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:02:01,662 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:02:01,686 - text_processor - INFO - 成功处理实体: 嘎布友哒露营基地
2025-07-24 00:02:01,686 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:02:01,687 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:02:01,794 - __main__ - INFO - 处理子批次 28/34
2025-07-24 00:02:01,794 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:02:01,794 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:02:15,778 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:02:24,647 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:02:24,676 - text_processor - INFO - 成功处理实体: 城北萨斯格桑林卡
2025-07-24 00:02:32,028 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:02:40,668 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:02:40,682 - text_processor - INFO - 成功处理实体: 西藏唐卡画院
2025-07-24 00:02:46,774 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:02:59,999 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:03:00,023 - text_processor - INFO - 成功处理实体: 拉萨奇趣昆虫科普展
2025-07-24 00:03:00,023 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:03:00,024 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:03:00,139 - __main__ - INFO - 处理子批次 29/34
2025-07-24 00:03:00,140 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:03:00,140 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:03:30,336 - text_processor - ERROR - LLM 调用失败: 
2025-07-24 00:03:39,834 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:03:51,535 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:03:51,550 - text_processor - INFO - 成功处理实体: 吞米桑布扎雕像
2025-07-24 00:04:04,328 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:04:09,737 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:04:09,750 - text_processor - INFO - 成功处理实体: 涉溪山谷露营地
2025-07-24 00:04:17,486 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:04:22,719 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:04:22,731 - text_processor - INFO - 成功处理实体: 尼木吞巴非遗中心
2025-07-24 00:04:22,732 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:04:22,732 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:04:22,844 - __main__ - INFO - 处理子批次 30/34
2025-07-24 00:04:22,844 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:04:22,845 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:04:31,366 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:04:50,411 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:04:50,454 - text_processor - INFO - 成功处理实体: 群巴
2025-07-24 00:04:54,551 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:05:01,748 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:05:01,764 - text_processor - INFO - 成功处理实体: 热卡扎日追寺
2025-07-24 00:05:15,841 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:05:21,690 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:05:21,709 - text_processor - INFO - 成功处理实体: 李四光雕像
2025-07-24 00:05:21,710 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:05:21,710 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:05:21,821 - __main__ - INFO - 处理子批次 31/34
2025-07-24 00:05:21,821 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:05:21,821 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:05:42,571 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:05:48,384 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:05:48,408 - text_processor - INFO - 成功处理实体: 曲苏
2025-07-24 00:05:56,149 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:06:04,088 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:06:04,109 - text_processor - INFO - 成功处理实体: 藏汉断桥玻璃阳光房
2025-07-24 00:06:12,830 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:06:24,788 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:06:24,800 - text_processor - INFO - 成功处理实体: 梦创拉萨
2025-07-24 00:06:24,800 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:06:24,800 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:06:24,917 - __main__ - INFO - 处理子批次 32/34
2025-07-24 00:06:24,917 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:06:24,918 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:06:31,989 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:06:40,119 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:06:40,132 - text_processor - INFO - 成功处理实体: 玛日扎扎
2025-07-24 00:06:50,162 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:06:59,864 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:06:59,924 - text_processor - INFO - 成功处理实体: 雪格拉山
2025-07-24 00:07:10,323 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:07:23,217 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:07:23,233 - text_processor - INFO - 成功处理实体: 密宗院
2025-07-24 00:07:23,233 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:07:23,234 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:07:23,346 - __main__ - INFO - 处理子批次 33/34
2025-07-24 00:07:23,347 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:07:23,347 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:07:29,394 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:07:36,955 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:07:37,000 - text_processor - INFO - 成功处理实体: 科比台球纪念馆
2025-07-24 00:07:43,052 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:07:54,626 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:07:54,642 - text_processor - INFO - 成功处理实体: 贡吉嘎彩林卡
2025-07-24 00:08:02,076 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:08:10,019 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:08:10,043 - text_processor - INFO - 成功处理实体: 颂吉东阁大昭寺店
2025-07-24 00:08:10,043 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:08:10,044 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:08:10,160 - __main__ - INFO - 处理子批次 34/34
2025-07-24 00:08:10,160 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:08:10,160 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:08:13,798 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:08:17,742 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:08:17,759 - text_processor - INFO - 成功处理实体: 水木林卡
2025-07-24 00:08:17,759 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:08:17,761 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:08:17,871 - __main__ - INFO - 批次 batch_100_nodes 完成: 100/100 成功, 耗时: 1410.88秒
2025-07-24 00:08:17,871 - __main__ - INFO - 批次 100 结果:
2025-07-24 00:08:17,871 - __main__ - INFO -   - 成功: 100/100
2025-07-24 00:08:17,871 - __main__ - INFO -   - 耗时: 1410.88 秒
2025-07-24 00:08:17,872 - __main__ - INFO -   - 速度: 0.07 节点/秒
2025-07-24 00:08:19,885 - __main__ - INFO - 
==================================================
2025-07-24 00:08:19,885 - __main__ - INFO - 开始批次实验: 150 个节点
2025-07-24 00:08:19,886 - __main__ - INFO - ==================================================
2025-07-24 00:08:19,913 - neo4j_connection - INFO - 清空所有节点和关系
2025-07-24 00:08:19,915 - neo4j_connection - INFO - 清空所有节点和关系
2025-07-24 00:08:19,915 - text_processor - INFO - 清空所有节点和关系
2025-07-24 00:08:19,918 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX attraction_name IF NOT EXISTS FOR (e:Attraction) ON (e.name)` has no effect.} {description: `RANGE INDEX attraction_name FOR (e:Attraction) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)'
2025-07-24 00:08:19,922 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX city_name IF NOT EXISTS FOR (e:City) ON (e.name)` has no effect.} {description: `RANGE INDEX city_name FOR (e:City) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)'
2025-07-24 00:08:19,922 - text_processor - INFO - 创建 Attraction 和 City 名称索引
2025-07-24 00:08:19,922 - __main__ - INFO - 数据库已清空并重置
2025-07-24 00:08:19,923 - __main__ - INFO - 开始异步导入批次: batch_150_nodes, 节点数: 150
2025-07-24 00:08:19,923 - __main__ - INFO - 处理子批次 1/50
2025-07-24 00:08:19,923 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:08:19,923 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:08:26,154 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:08:26,232 - text_processor - INFO - 成功处理实体: 布达拉宫
2025-07-24 00:08:30,389 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:08:30,402 - text_processor - INFO - 成功处理实体: 大昭寺
2025-07-24 00:08:36,106 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:08:36,120 - text_processor - INFO - 成功处理实体: 纳木措
2025-07-24 00:08:36,120 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:08:40,146 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:08:43,034 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:08:43,599 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:08:43,723 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:08:43,826 - __main__ - INFO - 处理子批次 2/50
2025-07-24 00:08:43,826 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:08:43,826 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:08:54,921 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:08:55,011 - text_processor - INFO - 成功处理实体: 色拉寺
2025-07-24 00:09:03,402 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:09:03,414 - text_processor - INFO - 成功处理实体: 八廓街
2025-07-24 00:09:33,602 - text_processor - ERROR - LLM 调用失败: 
2025-07-24 00:09:42,943 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:09:50,441 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:09:50,484 - text_processor - INFO - 成功处理实体: 《文成公主》大型史诗剧
2025-07-24 00:09:50,484 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:10:00,793 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:10:01,268 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:10:02,836 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:10:02,910 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:10:03,016 - __main__ - INFO - 处理子批次 3/50
2025-07-24 00:10:03,016 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:10:03,016 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:10:13,493 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:10:13,512 - text_processor - INFO - 成功处理实体: 罗布林卡
2025-07-24 00:10:17,629 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:10:17,658 - text_processor - INFO - 成功处理实体: 药王山
2025-07-24 00:10:23,404 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:10:23,424 - text_processor - INFO - 成功处理实体: 宗角禄康公园
2025-07-24 00:10:23,424 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:10:31,526 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:10:33,101 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:10:34,275 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:10:34,315 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:10:34,419 - __main__ - INFO - 处理子批次 4/50
2025-07-24 00:10:34,419 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:10:34,419 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:10:39,409 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:10:39,425 - text_processor - INFO - 成功处理实体: 哲蚌寺
2025-07-24 00:10:42,641 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:10:42,661 - text_processor - INFO - 成功处理实体: 小昭寺
2025-07-24 00:10:49,031 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:10:49,059 - text_processor - INFO - 成功处理实体: 西藏博物馆
2025-07-24 00:10:49,059 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:10:52,317 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:10:54,894 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:10:59,046 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:10:59,191 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:10:59,297 - __main__ - INFO - 处理子批次 5/50
2025-07-24 00:10:59,297 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:10:59,297 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:11:08,484 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:11:27,028 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:11:27,078 - text_processor - INFO - 成功处理实体: 娘热民俗风情园
2025-07-24 00:11:34,341 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:11:34,360 - text_processor - INFO - 成功处理实体: 仓姑寺
2025-07-24 00:11:42,749 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:11:42,763 - text_processor - INFO - 成功处理实体: 扎基寺
2025-07-24 00:11:42,763 - text_processor - INFO - 过滤后景点数量: 2
2025-07-24 00:11:53,457 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:11:53,481 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:11:53,589 - __main__ - INFO - 处理子批次 6/50
2025-07-24 00:11:53,589 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:11:53,589 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:12:00,396 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:12:00,418 - text_processor - INFO - 成功处理实体: 西藏拉萨清真大寺
2025-07-24 00:12:04,826 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:12:04,855 - text_processor - INFO - 成功处理实体: 拉萨河
2025-07-24 00:12:16,660 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:12:16,693 - text_processor - INFO - 成功处理实体: 帕邦喀
2025-07-24 00:12:16,693 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:12:24,005 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:12:25,232 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:12:27,954 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:12:27,988 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:12:28,103 - __main__ - INFO - 处理子批次 7/50
2025-07-24 00:12:28,104 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:12:28,105 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:12:33,241 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:12:33,287 - text_processor - INFO - 成功处理实体: 拉鲁湿地国家级自然保护区
2025-07-24 00:12:36,552 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:12:36,567 - text_processor - INFO - 成功处理实体: 喜德寺
2025-07-24 00:12:40,344 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:12:40,372 - text_processor - INFO - 成功处理实体: 羊八井
2025-07-24 00:12:40,373 - text_processor - INFO - 过滤后景点数量: 2
2025-07-24 00:12:45,395 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:12:45,400 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:12:45,502 - __main__ - INFO - 处理子批次 8/50
2025-07-24 00:12:45,502 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:12:45,502 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:12:51,690 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:12:57,034 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:12:57,083 - text_processor - INFO - 成功处理实体: 直贡噶举派寺庙群
2025-07-24 00:13:01,652 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:13:01,664 - text_processor - INFO - 成功处理实体: 甘丹寺
2025-07-24 00:13:07,812 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:13:07,827 - text_processor - INFO - 成功处理实体: 米拉山口
2025-07-24 00:13:07,828 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:13:19,943 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:13:22,083 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:13:38,104 - text_processor - ERROR - LLM 调用失败: 
2025-07-24 00:13:51,001 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:13:51,046 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:13:51,153 - __main__ - INFO - 处理子批次 9/50
2025-07-24 00:13:51,153 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:13:51,153 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:13:55,207 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:13:55,223 - text_processor - INFO - 成功处理实体: 和平解放纪念碑
2025-07-24 00:13:58,462 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:13:58,474 - text_processor - INFO - 成功处理实体: 布达拉宫广场
2025-07-24 00:14:03,679 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:14:03,691 - text_processor - INFO - 成功处理实体: 那根拉山口
2025-07-24 00:14:03,692 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:14:09,075 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:14:15,103 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:15:27,903 - neo4j_connection - INFO - 初始化 Neo4j 连接: bolt://localhost:7687, 用户: neo4j
2025-07-24 00:15:27,917 - neo4j_connection - INFO - Neo4j 连接验证成功
2025-07-24 00:15:27,917 - __main__ - INFO - 成功连接到Neo4j数据库
2025-07-24 00:15:27,917 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:15:27,917 - __main__ - INFO - 开始分批异步导入实验，批次大小: [10, 50, 100, 150, 200, 250]
2025-07-24 00:15:27,919 - __main__ - INFO - 读取到 260 个节点
2025-07-24 00:15:27,919 - __main__ - INFO - 去重后节点数: 258
2025-07-24 00:15:27,919 - __main__ - INFO - 
==================================================
2025-07-24 00:15:27,919 - __main__ - INFO - 开始批次实验: 10 个节点
2025-07-24 00:15:27,919 - __main__ - INFO - ==================================================
2025-07-24 00:15:27,926 - neo4j_connection - INFO - 清空所有节点和关系
2025-07-24 00:15:27,929 - neo4j_connection - INFO - 清空所有节点和关系
2025-07-24 00:15:27,929 - text_processor - INFO - 清空所有节点和关系
2025-07-24 00:15:27,930 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX attraction_name IF NOT EXISTS FOR (e:Attraction) ON (e.name)` has no effect.} {description: `RANGE INDEX attraction_name FOR (e:Attraction) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)'
2025-07-24 00:15:27,934 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX city_name IF NOT EXISTS FOR (e:City) ON (e.name)` has no effect.} {description: `RANGE INDEX city_name FOR (e:City) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)'
2025-07-24 00:15:27,934 - text_processor - INFO - 创建 Attraction 和 City 名称索引
2025-07-24 00:15:27,934 - __main__ - INFO - 数据库已清空并重置
2025-07-24 00:15:27,934 - __main__ - INFO - 开始异步导入批次: batch_10_nodes, 节点数: 10
2025-07-24 00:15:27,935 - __main__ - INFO - 处理子批次 1/4
2025-07-24 00:15:27,936 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:15:27,936 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:15:34,323 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:15:34,390 - text_processor - INFO - 成功处理实体: 布达拉宫
2025-07-24 00:15:40,000 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:15:40,021 - text_processor - INFO - 成功处理实体: 大昭寺
2025-07-24 00:15:44,107 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:15:44,126 - text_processor - INFO - 成功处理实体: 纳木措
2025-07-24 00:15:44,126 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:15:49,913 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:15:50,536 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:15:51,246 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:15:51,367 - neo4j_crud - ERROR - Cypher 语法错误: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input ']': expected '&', ')', ':' or '|' (line 4, column 43 (offset: 161))

"            MERGE (source)-[r:(IEnumerable]->(target)"

                                           ^}, 查询: 
            MATCH (source:Attraction {name: $source_name})
            MATCH (target:Attraction {name: $target_name})
            MERGE (source)-[r:(IEnumerable]->(target)
            SET r += $props
            RETURN r
            , 参数: 布达拉宫 -> 纳木措
2025-07-24 00:15:51,398 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:15:51,514 - __main__ - INFO - 处理子批次 2/4
2025-07-24 00:15:51,514 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:15:51,514 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:15:58,518 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:15:58,583 - text_processor - INFO - 成功处理实体: 色拉寺
2025-07-24 00:16:03,183 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:16:03,195 - text_processor - INFO - 成功处理实体: 八廓街
2025-07-24 00:16:08,867 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:16:16,186 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:16:16,231 - text_processor - INFO - 成功处理实体: 《文成公主》大型史诗剧
2025-07-24 00:16:16,231 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:16:22,838 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:16:22,900 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:16:25,336 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:16:25,363 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:16:25,470 - __main__ - INFO - 处理子批次 3/4
2025-07-24 00:16:25,470 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:16:25,470 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:16:29,802 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:16:29,816 - text_processor - INFO - 成功处理实体: 罗布林卡
2025-07-24 00:16:34,145 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:16:34,167 - text_processor - INFO - 成功处理实体: 药王山
2025-07-24 00:16:42,415 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:16:42,428 - text_processor - INFO - 成功处理实体: 宗角禄康公园
2025-07-24 00:16:42,428 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:16:47,838 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:16:51,020 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:16:55,242 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:16:55,378 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:16:55,486 - __main__ - INFO - 处理子批次 4/4
2025-07-24 00:16:55,486 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:16:55,486 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:17:05,557 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:17:05,575 - text_processor - INFO - 成功处理实体: 哲蚌寺
2025-07-24 00:17:05,575 - text_processor - INFO - 过滤后景点数量: 1
2025-07-24 00:17:05,577 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:17:05,683 - __main__ - INFO - 批次 batch_10_nodes 完成: 10/10 成功, 耗时: 97.75秒
2025-07-24 00:17:05,684 - __main__ - INFO - 批次 10 结果:
2025-07-24 00:17:05,684 - __main__ - INFO -   - 成功: 10/10
2025-07-24 00:17:05,684 - __main__ - INFO -   - 耗时: 97.75 秒
2025-07-24 00:17:05,684 - __main__ - INFO -   - 速度: 0.10 节点/秒
2025-07-24 00:17:07,687 - __main__ - INFO - 
==================================================
2025-07-24 00:17:07,687 - __main__ - INFO - 开始批次实验: 50 个节点
2025-07-24 00:17:07,687 - __main__ - INFO - ==================================================
2025-07-24 00:17:07,714 - neo4j_connection - INFO - 清空所有节点和关系
2025-07-24 00:17:07,716 - neo4j_connection - INFO - 清空所有节点和关系
2025-07-24 00:17:07,716 - text_processor - INFO - 清空所有节点和关系
2025-07-24 00:17:07,719 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX attraction_name IF NOT EXISTS FOR (e:Attraction) ON (e.name)` has no effect.} {description: `RANGE INDEX attraction_name FOR (e:Attraction) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)'
2025-07-24 00:17:07,721 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX city_name IF NOT EXISTS FOR (e:City) ON (e.name)` has no effect.} {description: `RANGE INDEX city_name FOR (e:City) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)'
2025-07-24 00:17:07,721 - text_processor - INFO - 创建 Attraction 和 City 名称索引
2025-07-24 00:17:07,721 - __main__ - INFO - 数据库已清空并重置
2025-07-24 00:17:07,722 - __main__ - INFO - 开始异步导入批次: batch_50_nodes, 节点数: 50
2025-07-24 00:17:07,722 - __main__ - INFO - 处理子批次 1/17
2025-07-24 00:17:07,722 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:17:07,722 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:17:14,702 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:17:14,725 - text_processor - INFO - 成功处理实体: 布达拉宫
2025-07-24 00:17:19,084 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:17:19,143 - text_processor - INFO - 成功处理实体: 大昭寺
2025-07-24 00:17:23,283 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:17:23,311 - text_processor - INFO - 成功处理实体: 纳木措
2025-07-24 00:17:23,312 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:17:27,404 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:17:34,900 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:17:35,805 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:17:35,918 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:17:36,030 - __main__ - INFO - 处理子批次 2/17
2025-07-24 00:17:36,031 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:17:36,032 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:17:43,049 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:17:43,116 - text_processor - INFO - 成功处理实体: 色拉寺
2025-07-24 00:17:47,005 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:17:47,025 - text_processor - INFO - 成功处理实体: 八廓街
2025-07-24 00:17:50,042 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:17:57,642 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:17:57,658 - text_processor - INFO - 成功处理实体: 《文成公主》大型史诗剧
2025-07-24 00:17:57,658 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:18:09,862 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:18:10,698 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:18:20,038 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:18:20,085 - text_processor - ERROR - 创建关系失败: 色拉寺 -> 《文成公主》大型史诗剧: Expecting value: line 1 column 1 (char 0)
2025-07-24 00:18:20,102 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:18:20,219 - __main__ - INFO - 处理子批次 3/17
2025-07-24 00:18:20,220 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:18:20,220 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:18:26,703 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:18:26,719 - text_processor - INFO - 成功处理实体: 罗布林卡
2025-07-24 00:18:33,082 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:18:33,105 - text_processor - INFO - 成功处理实体: 药王山
2025-07-24 00:18:39,025 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:18:39,048 - text_processor - INFO - 成功处理实体: 宗角禄康公园
2025-07-24 00:18:39,049 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:18:42,146 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:18:50,428 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:18:52,193 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:18:52,196 - text_processor - ERROR - 创建关系失败: 罗布林卡 -> 药王山: Expecting value: line 1 column 1 (char 0)
2025-07-24 00:18:52,216 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:18:52,327 - __main__ - INFO - 处理子批次 4/17
2025-07-24 00:18:52,327 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:18:52,327 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:18:56,660 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:18:56,707 - text_processor - INFO - 成功处理实体: 哲蚌寺
2025-07-24 00:18:59,994 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:19:00,006 - text_processor - INFO - 成功处理实体: 小昭寺
2025-07-24 00:19:04,686 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:19:04,700 - text_processor - INFO - 成功处理实体: 西藏博物馆
2025-07-24 00:19:04,700 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:19:15,058 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:19:19,016 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:19:26,349 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:19:26,472 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:19:26,574 - __main__ - INFO - 处理子批次 5/17
2025-07-24 00:19:26,574 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:19:26,575 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:19:30,343 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:19:41,489 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:19:41,547 - text_processor - INFO - 成功处理实体: 娘热民俗风情园
2025-07-24 00:19:46,326 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:19:46,339 - text_processor - INFO - 成功处理实体: 仓姑寺
2025-07-24 00:19:51,994 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:19:52,011 - text_processor - INFO - 成功处理实体: 扎基寺
2025-07-24 00:19:52,011 - text_processor - INFO - 过滤后景点数量: 2
2025-07-24 00:19:59,379 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:19:59,431 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:19:59,535 - __main__ - INFO - 处理子批次 6/17
2025-07-24 00:19:59,537 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:19:59,538 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:20:04,220 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:20:04,260 - text_processor - INFO - 成功处理实体: 西藏拉萨清真大寺
2025-07-24 00:20:08,241 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:20:08,265 - text_processor - INFO - 成功处理实体: 拉萨河
2025-07-24 00:20:13,278 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:20:13,293 - text_processor - INFO - 成功处理实体: 帕邦喀
2025-07-24 00:20:13,293 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:20:19,276 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:20:21,257 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:20:21,441 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:20:21,487 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:20:21,592 - __main__ - INFO - 处理子批次 7/17
2025-07-24 00:20:21,592 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:20:21,592 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:20:26,050 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:20:26,140 - text_processor - INFO - 成功处理实体: 拉鲁湿地国家级自然保护区
2025-07-24 00:20:31,202 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:20:31,217 - text_processor - INFO - 成功处理实体: 喜德寺
2025-07-24 00:20:35,827 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:20:35,837 - text_processor - INFO - 成功处理实体: 羊八井
2025-07-24 00:20:35,837 - text_processor - INFO - 过滤后景点数量: 2
2025-07-24 00:20:41,363 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:20:41,370 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:20:41,483 - __main__ - INFO - 处理子批次 8/17
2025-07-24 00:20:41,483 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:20:41,484 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:20:47,057 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:20:58,160 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:20:58,194 - text_processor - INFO - 成功处理实体: 直贡噶举派寺庙群
2025-07-24 00:21:05,039 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:21:05,059 - text_processor - INFO - 成功处理实体: 甘丹寺
2025-07-24 00:21:09,212 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:21:09,224 - text_processor - INFO - 成功处理实体: 米拉山口
2025-07-24 00:21:09,225 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:21:15,025 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:21:16,245 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:21:21,831 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:21:21,886 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:21:21,999 - __main__ - INFO - 处理子批次 9/17
2025-07-24 00:21:21,999 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:21:21,999 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:21:27,877 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:21:27,910 - text_processor - INFO - 成功处理实体: 和平解放纪念碑
2025-07-24 00:21:33,019 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:21:33,033 - text_processor - INFO - 成功处理实体: 布达拉宫广场
2025-07-24 00:21:39,079 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:21:39,099 - text_processor - INFO - 成功处理实体: 那根拉山口
2025-07-24 00:21:39,099 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:21:46,899 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:21:48,108 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:21:49,724 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:21:49,762 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:21:49,878 - __main__ - INFO - 处理子批次 10/17
2025-07-24 00:21:49,878 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:21:49,878 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:21:54,810 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:21:54,835 - text_processor - INFO - 成功处理实体: 木如寺印经院
2025-07-24 00:22:04,571 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:22:04,584 - text_processor - INFO - 成功处理实体: 下密寺
2025-07-24 00:22:09,762 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:22:09,774 - text_processor - INFO - 成功处理实体: 驻藏大臣衙门遗址
2025-07-24 00:22:09,774 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:22:17,187 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:22:17,299 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:22:18,571 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:22:18,619 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:22:18,727 - __main__ - INFO - 处理子批次 11/17
2025-07-24 00:22:18,728 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:22:18,728 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:22:23,482 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:22:23,516 - text_processor - INFO - 成功处理实体: 小昭寺路
2025-07-24 00:22:29,648 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:22:40,587 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:22:40,600 - text_processor - INFO - 成功处理实体: 拉萨民族文化艺术宫
2025-07-24 00:22:48,157 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:23:00,701 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:23:00,726 - text_processor - INFO - 成功处理实体: 5238
2025-07-24 00:23:00,726 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:23:00,727 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:23:00,833 - __main__ - INFO - 处理子批次 12/17
2025-07-24 00:23:00,833 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:23:00,834 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:23:06,162 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:23:14,762 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:23:14,777 - text_processor - INFO - 成功处理实体: 纳木措扎西岛
2025-07-24 00:23:21,919 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:23:31,473 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:23:31,496 - text_processor - INFO - 成功处理实体: 西藏藏医药文化博览中心
2025-07-24 00:23:34,916 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:23:34,928 - text_processor - INFO - 成功处理实体: 达扎路恭纪功碑
2025-07-24 00:23:34,928 - text_processor - INFO - 过滤后景点数量: 1
2025-07-24 00:23:34,928 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:23:35,038 - __main__ - INFO - 处理子批次 13/17
2025-07-24 00:23:35,038 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:23:35,038 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:23:40,476 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:23:46,907 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:23:46,954 - text_processor - INFO - 成功处理实体: 甲玛王宫
2025-07-24 00:23:50,393 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:23:50,413 - text_processor - INFO - 成功处理实体: 扎希寺
2025-07-24 00:23:54,799 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:23:54,819 - text_processor - INFO - 成功处理实体: 楚布寺
2025-07-24 00:23:54,819 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:23:54,820 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:23:54,934 - __main__ - INFO - 处理子批次 14/17
2025-07-24 00:23:54,934 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:23:54,934 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:23:59,994 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:24:00,034 - text_processor - INFO - 成功处理实体: 松赞干布出生地
2025-07-24 00:24:08,819 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:24:08,840 - text_processor - INFO - 成功处理实体: 聂当度母殿和聂当大佛
2025-07-24 00:24:10,657 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:24:10,702 - text_processor - INFO - 成功处理实体: 策门林寺
2025-07-24 00:24:10,703 - text_processor - INFO - 过滤后景点数量: 1
2025-07-24 00:24:10,703 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:24:10,812 - __main__ - INFO - 处理子批次 15/17
2025-07-24 00:24:10,812 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:24:10,812 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:24:15,387 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:24:15,426 - text_processor - INFO - 成功处理实体: 鲁普岩寺
2025-07-24 00:24:19,270 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:24:19,289 - text_processor - INFO - 成功处理实体: 仙足岛
2025-07-24 00:24:22,477 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:24:22,497 - text_processor - INFO - 成功处理实体: 曲贡古遗址
2025-07-24 00:24:22,499 - text_processor - INFO - 过滤后景点数量: 1
2025-07-24 00:24:22,499 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:24:22,615 - __main__ - INFO - 处理子批次 16/17
2025-07-24 00:24:22,615 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:24:22,615 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:24:25,702 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:24:25,735 - text_processor - INFO - 成功处理实体: 乃琼寺
2025-07-24 00:24:30,648 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:24:30,669 - text_processor - INFO - 成功处理实体: 次巴拉康寺
2025-07-24 00:24:37,582 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:24:37,605 - text_processor - INFO - 成功处理实体: 清政府驻藏大臣衙门旧址
2025-07-24 00:24:37,605 - text_processor - INFO - 过滤后景点数量: 1
2025-07-24 00:24:37,606 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:24:37,721 - __main__ - INFO - 处理子批次 17/17
2025-07-24 00:24:37,721 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:24:37,721 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:24:40,929 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:24:45,615 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:24:45,650 - text_processor - INFO - 成功处理实体: 热振寺
2025-07-24 00:24:49,427 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:24:49,441 - text_processor - INFO - 成功处理实体: 关帝庙
2025-07-24 00:24:49,441 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:24:49,442 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:24:49,549 - __main__ - INFO - 批次 batch_50_nodes 完成: 50/50 成功, 耗时: 461.83秒
2025-07-24 00:24:49,549 - __main__ - INFO - 批次 50 结果:
2025-07-24 00:24:49,550 - __main__ - INFO -   - 成功: 50/50
2025-07-24 00:24:49,550 - __main__ - INFO -   - 耗时: 461.83 秒
2025-07-24 00:24:49,550 - __main__ - INFO -   - 速度: 0.11 节点/秒
2025-07-24 00:24:51,551 - __main__ - INFO - 
==================================================
2025-07-24 00:24:51,551 - __main__ - INFO - 开始批次实验: 100 个节点
2025-07-24 00:24:51,552 - __main__ - INFO - ==================================================
2025-07-24 00:24:51,567 - neo4j_connection - INFO - 清空所有节点和关系
2025-07-24 00:24:51,568 - neo4j_connection - INFO - 清空所有节点和关系
2025-07-24 00:24:51,569 - text_processor - INFO - 清空所有节点和关系
2025-07-24 00:24:51,571 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX attraction_name IF NOT EXISTS FOR (e:Attraction) ON (e.name)` has no effect.} {description: `RANGE INDEX attraction_name FOR (e:Attraction) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)'
2025-07-24 00:24:51,573 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX city_name IF NOT EXISTS FOR (e:City) ON (e.name)` has no effect.} {description: `RANGE INDEX city_name FOR (e:City) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)'
2025-07-24 00:24:51,574 - text_processor - INFO - 创建 Attraction 和 City 名称索引
2025-07-24 00:24:51,574 - __main__ - INFO - 数据库已清空并重置
2025-07-24 00:24:51,574 - __main__ - INFO - 开始异步导入批次: batch_100_nodes, 节点数: 100
2025-07-24 00:24:51,574 - __main__ - INFO - 处理子批次 1/34
2025-07-24 00:24:51,574 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:24:51,574 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:24:59,438 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:24:59,517 - text_processor - INFO - 成功处理实体: 布达拉宫
2025-07-24 00:25:06,870 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:25:06,882 - text_processor - INFO - 成功处理实体: 大昭寺
2025-07-24 00:25:11,449 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:25:11,516 - text_processor - INFO - 成功处理实体: 纳木措
2025-07-24 00:25:11,517 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:25:15,881 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:25:17,418 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:25:19,289 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:25:19,369 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:25:19,473 - __main__ - INFO - 处理子批次 2/34
2025-07-24 00:25:19,474 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:25:19,474 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:25:24,837 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:25:24,867 - text_processor - INFO - 成功处理实体: 色拉寺
2025-07-24 00:25:32,103 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:25:32,115 - text_processor - INFO - 成功处理实体: 八廓街
2025-07-24 00:25:36,645 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:25:46,297 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:25:46,338 - text_processor - INFO - 成功处理实体: 《文成公主》大型史诗剧
2025-07-24 00:25:46,339 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:25:53,002 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:25:53,954 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:25:54,793 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:25:54,856 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:25:54,971 - __main__ - INFO - 处理子批次 3/34
2025-07-24 00:25:54,971 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:25:54,972 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:25:58,959 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:25:59,005 - text_processor - INFO - 成功处理实体: 罗布林卡
2025-07-24 00:26:03,127 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:26:03,139 - text_processor - INFO - 成功处理实体: 药王山
2025-07-24 00:26:07,296 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:26:07,310 - text_processor - INFO - 成功处理实体: 宗角禄康公园
2025-07-24 00:26:07,310 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:26:10,038 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:26:16,051 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:26:22,190 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:26:22,243 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:26:22,344 - __main__ - INFO - 处理子批次 4/34
2025-07-24 00:26:22,344 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:26:22,344 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:26:27,304 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:26:27,343 - text_processor - INFO - 成功处理实体: 哲蚌寺
2025-07-24 00:26:37,786 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:26:37,798 - text_processor - INFO - 成功处理实体: 小昭寺
2025-07-24 00:26:45,595 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:26:45,618 - text_processor - INFO - 成功处理实体: 西藏博物馆
2025-07-24 00:26:45,619 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:26:51,322 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:26:54,437 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:27:03,723 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:27:03,863 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:27:03,977 - __main__ - INFO - 处理子批次 5/34
2025-07-24 00:27:03,977 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:27:03,977 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:27:13,148 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:27:21,342 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:27:21,404 - text_processor - INFO - 成功处理实体: 娘热民俗风情园
2025-07-24 00:27:24,754 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:27:24,767 - text_processor - INFO - 成功处理实体: 仓姑寺
2025-07-24 00:27:28,104 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:27:28,116 - text_processor - INFO - 成功处理实体: 扎基寺
2025-07-24 00:27:28,116 - text_processor - INFO - 过滤后景点数量: 2
2025-07-24 00:27:33,902 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:27:33,920 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:27:34,034 - __main__ - INFO - 处理子批次 6/34
2025-07-24 00:27:34,034 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:27:34,035 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:27:37,829 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:27:37,863 - text_processor - INFO - 成功处理实体: 西藏拉萨清真大寺
2025-07-24 00:27:43,357 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:27:43,369 - text_processor - INFO - 成功处理实体: 拉萨河
2025-07-24 00:27:46,003 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:27:46,019 - text_processor - INFO - 成功处理实体: 帕邦喀
2025-07-24 00:27:46,019 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:27:51,737 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:27:51,804 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:27:52,603 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:27:52,652 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:27:52,767 - __main__ - INFO - 处理子批次 7/34
2025-07-24 00:27:52,767 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:27:52,767 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:27:56,051 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:27:56,133 - text_processor - INFO - 成功处理实体: 拉鲁湿地国家级自然保护区
2025-07-24 00:28:05,579 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:28:05,592 - text_processor - INFO - 成功处理实体: 喜德寺
2025-07-24 00:28:08,709 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:28:08,734 - text_processor - INFO - 成功处理实体: 羊八井
2025-07-24 00:28:08,735 - text_processor - INFO - 过滤后景点数量: 2
2025-07-24 00:28:15,967 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:28:15,972 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:28:16,078 - __main__ - INFO - 处理子批次 8/34
2025-07-24 00:28:16,078 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:28:16,079 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:28:21,610 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:28:30,762 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:28:30,776 - text_processor - INFO - 成功处理实体: 直贡噶举派寺庙群
2025-07-24 00:28:39,567 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:28:39,587 - text_processor - INFO - 成功处理实体: 甘丹寺
2025-07-24 00:28:41,046 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:28:41,057 - text_processor - INFO - 成功处理实体: 米拉山口
2025-07-24 00:28:41,058 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:28:46,104 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:28:47,051 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:28:54,479 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:28:54,548 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:28:54,664 - __main__ - INFO - 处理子批次 9/34
2025-07-24 00:28:54,664 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:28:54,664 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:29:00,092 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:29:00,104 - text_processor - INFO - 成功处理实体: 和平解放纪念碑
2025-07-24 00:29:08,502 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:29:08,514 - text_processor - INFO - 成功处理实体: 布达拉宫广场
2025-07-24 00:29:14,030 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:29:14,046 - text_processor - INFO - 成功处理实体: 那根拉山口
2025-07-24 00:29:14,047 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:29:18,731 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:29:19,568 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:29:25,667 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:29:25,692 - text_processor - ERROR - 创建关系失败: 布达拉宫广场 -> 那根拉山口: Extra data: line 1 column 4 (char 3)
2025-07-24 00:29:25,693 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:29:25,803 - __main__ - INFO - 处理子批次 10/34
2025-07-24 00:29:25,803 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:29:25,804 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:29:31,274 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:29:31,311 - text_processor - INFO - 成功处理实体: 木如寺印经院
2025-07-24 00:29:37,399 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:29:37,419 - text_processor - INFO - 成功处理实体: 下密寺
2025-07-24 00:29:41,166 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:29:41,178 - text_processor - INFO - 成功处理实体: 驻藏大臣衙门遗址
2025-07-24 00:29:41,178 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:29:46,991 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:29:47,738 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:29:52,482 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:29:52,517 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:29:52,626 - __main__ - INFO - 处理子批次 11/34
2025-07-24 00:29:52,626 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:29:52,627 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:29:55,835 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:29:55,873 - text_processor - INFO - 成功处理实体: 小昭寺路
2025-07-24 00:30:08,026 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:30:14,712 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:30:14,725 - text_processor - INFO - 成功处理实体: 拉萨民族文化艺术宫
2025-07-24 00:30:22,806 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:30:31,775 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:30:31,788 - text_processor - INFO - 成功处理实体: 5238
2025-07-24 00:30:31,789 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:30:31,791 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:30:31,898 - __main__ - INFO - 处理子批次 12/34
2025-07-24 00:30:31,898 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:30:31,898 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:30:37,668 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:30:47,157 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:30:47,194 - text_processor - INFO - 成功处理实体: 纳木措扎西岛
2025-07-24 00:30:54,488 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:31:00,368 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:31:00,391 - text_processor - INFO - 成功处理实体: 西藏藏医药文化博览中心
2025-07-24 00:31:06,296 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:31:06,307 - text_processor - INFO - 成功处理实体: 达扎路恭纪功碑
2025-07-24 00:31:06,307 - text_processor - INFO - 过滤后景点数量: 1
2025-07-24 00:31:06,307 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:31:06,417 - __main__ - INFO - 处理子批次 13/34
2025-07-24 00:31:06,417 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:31:06,417 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:31:13,062 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:31:20,697 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:31:20,739 - text_processor - INFO - 成功处理实体: 甲玛王宫
2025-07-24 00:31:24,252 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:31:24,266 - text_processor - INFO - 成功处理实体: 扎希寺
2025-07-24 00:31:31,774 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:31:31,789 - text_processor - INFO - 成功处理实体: 楚布寺
2025-07-24 00:31:31,789 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:31:31,790 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:31:31,891 - __main__ - INFO - 处理子批次 14/34
2025-07-24 00:31:31,891 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:31:31,891 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:31:37,575 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:31:37,612 - text_processor - INFO - 成功处理实体: 松赞干布出生地
2025-07-24 00:31:44,520 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:31:44,532 - text_processor - INFO - 成功处理实体: 聂当度母殿和聂当大佛
2025-07-24 00:31:53,070 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:31:53,088 - text_processor - INFO - 成功处理实体: 策门林寺
2025-07-24 00:31:53,088 - text_processor - INFO - 过滤后景点数量: 1
2025-07-24 00:31:53,088 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:31:53,198 - __main__ - INFO - 处理子批次 15/34
2025-07-24 00:31:53,198 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:31:53,198 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:32:00,270 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:32:00,324 - text_processor - INFO - 成功处理实体: 鲁普岩寺
2025-07-24 00:32:05,573 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:32:05,584 - text_processor - INFO - 成功处理实体: 仙足岛
2025-07-24 00:32:14,039 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:32:14,059 - text_processor - INFO - 成功处理实体: 曲贡古遗址
2025-07-24 00:32:14,060 - text_processor - INFO - 过滤后景点数量: 1
2025-07-24 00:32:14,060 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:32:14,174 - __main__ - INFO - 处理子批次 16/34
2025-07-24 00:32:14,174 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:32:14,175 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:32:17,804 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:32:17,829 - text_processor - INFO - 成功处理实体: 乃琼寺
2025-07-24 00:32:20,999 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:32:21,011 - text_processor - INFO - 成功处理实体: 次巴拉康寺
2025-07-24 00:32:28,848 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:32:28,858 - text_processor - INFO - 成功处理实体: 清政府驻藏大臣衙门旧址
2025-07-24 00:32:28,858 - text_processor - INFO - 过滤后景点数量: 1
2025-07-24 00:32:28,859 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:32:28,971 - __main__ - INFO - 处理子批次 17/34
2025-07-24 00:32:28,971 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:32:28,971 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:32:35,981 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:32:40,180 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:32:40,213 - text_processor - INFO - 成功处理实体: 热振寺
2025-07-24 00:32:43,452 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:32:43,465 - text_processor - INFO - 成功处理实体: 关帝庙
2025-07-24 00:32:47,558 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:32:55,539 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:32:55,552 - text_processor - INFO - 成功处理实体: 喜德林
2025-07-24 00:32:55,553 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:32:55,553 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:32:55,668 - __main__ - INFO - 处理子批次 18/34
2025-07-24 00:32:55,669 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:32:55,669 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:33:11,233 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:33:22,217 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:33:22,249 - text_processor - INFO - 成功处理实体: 白色寺
2025-07-24 00:33:25,442 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:33:30,711 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:33:30,725 - text_processor - INFO - 成功处理实体: 西藏非遗博物馆附属建筑1栋
2025-07-24 00:33:35,446 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:33:41,177 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:33:41,189 - text_processor - INFO - 成功处理实体: 纳木寺
2025-07-24 00:33:41,190 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:33:41,190 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:33:41,305 - __main__ - INFO - 处理子批次 19/34
2025-07-24 00:33:41,305 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:33:41,305 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:33:54,370 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:34:03,274 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:34:03,311 - text_processor - INFO - 成功处理实体: 林周县楚杰寺
2025-07-24 00:34:17,105 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:34:24,442 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:34:24,463 - text_processor - INFO - 成功处理实体: 中华文化园露营地
2025-07-24 00:34:39,555 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:34:51,226 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:34:51,241 - text_processor - INFO - 成功处理实体: 秀色才纳净土博览园
2025-07-24 00:34:51,241 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:34:51,241 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:34:51,346 - __main__ - INFO - 处理子批次 20/34
2025-07-24 00:34:51,346 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:34:51,346 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:34:55,125 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:35:02,527 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:35:02,603 - text_processor - INFO - 成功处理实体: 茶马古道马帮落脚点遗址
2025-07-24 00:35:07,714 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:35:14,849 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:35:14,865 - text_processor - INFO - 成功处理实体: 山野星空露营-吉隆营地
2025-07-24 00:35:20,806 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:35:30,073 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:35:30,087 - text_processor - INFO - 成功处理实体: 奇秀园林
2025-07-24 00:35:30,087 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:35:30,087 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:35:30,203 - __main__ - INFO - 处理子批次 21/34
2025-07-24 00:35:30,203 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:35:30,203 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:35:44,364 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:35:51,896 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:35:51,930 - text_processor - INFO - 成功处理实体: 洛堆吉曲康桑区
2025-07-24 00:35:56,679 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:36:03,886 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:36:03,900 - text_processor - INFO - 成功处理实体: 格桑林卡
2025-07-24 00:36:10,042 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:36:16,133 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:36:16,147 - text_processor - INFO - 成功处理实体: 塔玉贡康寺
2025-07-24 00:36:16,148 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:36:16,148 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:36:16,257 - __main__ - INFO - 处理子批次 22/34
2025-07-24 00:36:16,257 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:36:16,257 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:36:26,628 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:36:35,196 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:36:35,227 - text_processor - INFO - 成功处理实体: 吉布寺
2025-07-24 00:36:43,004 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:36:50,473 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:36:50,495 - text_processor - INFO - 成功处理实体: 美珠杂日生态乐园
2025-07-24 00:36:55,767 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:37:09,083 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:37:09,096 - text_processor - INFO - 成功处理实体: 舒适的露营地
2025-07-24 00:37:09,096 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:37:09,097 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:37:09,210 - __main__ - INFO - 处理子批次 23/34
2025-07-24 00:37:09,210 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:37:09,211 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:37:19,202 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:37:25,946 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:37:25,972 - text_processor - INFO - 成功处理实体: 钱学森雕像
2025-07-24 00:37:32,971 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:37:39,750 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:37:39,766 - text_processor - INFO - 成功处理实体: 嘎卓卓休闲露营基地
2025-07-24 00:37:45,024 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:37:51,028 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:37:51,042 - text_processor - INFO - 成功处理实体: 明珠林
2025-07-24 00:37:51,043 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:37:51,043 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:37:51,157 - __main__ - INFO - 处理子批次 24/34
2025-07-24 00:37:51,157 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:37:51,157 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:37:59,131 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:38:05,203 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:38:05,233 - text_processor - INFO - 成功处理实体: 西藏林周草牧业科技小院
2025-07-24 00:38:10,155 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:38:17,511 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:38:17,531 - text_processor - INFO - 成功处理实体: 内马厩
2025-07-24 00:38:23,345 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:38:28,706 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:38:28,727 - text_processor - INFO - 成功处理实体: 尤隆布
2025-07-24 00:38:28,727 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:38:28,728 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:38:28,833 - __main__ - INFO - 处理子批次 25/34
2025-07-24 00:38:28,833 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:38:28,833 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:38:35,270 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:38:41,892 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:38:41,928 - text_processor - INFO - 成功处理实体: 龙日秋摩
2025-07-24 00:38:50,288 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:38:59,361 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:38:59,373 - text_processor - INFO - 成功处理实体: 洛巴机康
2025-07-24 00:39:04,495 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:39:09,231 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:39:09,251 - text_processor - INFO - 成功处理实体: 崩布朗
2025-07-24 00:39:09,251 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:39:09,252 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:39:09,368 - __main__ - INFO - 处理子批次 26/34
2025-07-24 00:39:09,369 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:39:09,369 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:39:14,141 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:39:19,451 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:39:19,483 - text_processor - INFO - 成功处理实体: 加错
2025-07-24 00:39:28,131 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:39:33,989 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:39:34,013 - text_processor - INFO - 成功处理实体: 达尔多拉
2025-07-24 00:39:39,172 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:39:49,449 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:39:49,464 - text_processor - INFO - 成功处理实体: 林周县杰堆寺
2025-07-24 00:39:49,465 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:39:49,465 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:39:49,573 - __main__ - INFO - 处理子批次 27/34
2025-07-24 00:39:49,573 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:39:49,573 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:39:54,916 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:40:05,084 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:40:05,125 - text_processor - INFO - 成功处理实体: 扎日阿布塘
2025-07-24 00:40:14,118 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:40:21,669 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:40:21,692 - text_processor - INFO - 成功处理实体: 城关区净土国家级旅游景区
2025-07-24 00:40:26,430 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:40:32,231 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:40:32,246 - text_processor - INFO - 成功处理实体: 嘎布友哒露营基地
2025-07-24 00:40:32,246 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:40:32,247 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:40:32,355 - __main__ - INFO - 处理子批次 28/34
2025-07-24 00:40:32,355 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:40:32,356 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:40:42,153 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:40:49,796 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:40:49,830 - text_processor - INFO - 成功处理实体: 城北萨斯格桑林卡
2025-07-24 00:41:00,141 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:41:14,297 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:41:14,309 - text_processor - INFO - 成功处理实体: 西藏唐卡画院
2025-07-24 00:41:21,288 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:41:31,362 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:41:31,387 - text_processor - INFO - 成功处理实体: 拉萨奇趣昆虫科普展
2025-07-24 00:41:31,387 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:41:31,388 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:41:31,496 - __main__ - INFO - 处理子批次 29/34
2025-07-24 00:41:31,497 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:41:31,497 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:41:37,932 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:41:46,945 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:41:46,988 - text_processor - INFO - 成功处理实体: 吞米桑布扎雕像
2025-07-24 00:41:51,599 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:41:57,444 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:41:57,457 - text_processor - INFO - 成功处理实体: 涉溪山谷露营地
2025-07-24 00:42:03,784 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:42:12,205 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:42:12,216 - text_processor - INFO - 成功处理实体: 尼木吞巴非遗中心
2025-07-24 00:42:12,217 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:42:12,217 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:42:12,324 - __main__ - INFO - 处理子批次 30/34
2025-07-24 00:42:12,325 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:42:12,325 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:42:20,472 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:42:28,510 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:42:28,525 - text_processor - INFO - 成功处理实体: 群巴
2025-07-24 00:42:34,853 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:42:45,129 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:42:45,145 - text_processor - INFO - 成功处理实体: 热卡扎日追寺
2025-07-24 00:42:50,555 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:43:04,280 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:43:04,296 - text_processor - INFO - 成功处理实体: 李四光雕像
2025-07-24 00:43:04,296 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:43:04,296 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:43:04,400 - __main__ - INFO - 处理子批次 31/34
2025-07-24 00:43:04,401 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:43:04,401 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:43:11,149 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:43:23,833 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:43:23,866 - text_processor - INFO - 成功处理实体: 曲苏
2025-07-24 00:43:30,771 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:43:36,684 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:43:36,697 - text_processor - INFO - 成功处理实体: 藏汉断桥玻璃阳光房
2025-07-24 00:43:43,892 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:43:50,747 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:43:50,769 - text_processor - INFO - 成功处理实体: 梦创拉萨
2025-07-24 00:43:50,769 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:43:50,770 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:43:50,876 - __main__ - INFO - 处理子批次 32/34
2025-07-24 00:43:50,877 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:43:50,877 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:43:54,934 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:44:04,296 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:44:04,335 - text_processor - INFO - 成功处理实体: 玛日扎扎
2025-07-24 00:44:11,041 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:44:16,113 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:44:16,126 - text_processor - INFO - 成功处理实体: 雪格拉山
2025-07-24 00:44:25,842 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:44:38,237 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:44:38,257 - text_processor - INFO - 成功处理实体: 密宗院
2025-07-24 00:44:38,258 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:44:38,258 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:44:38,371 - __main__ - INFO - 处理子批次 33/34
2025-07-24 00:44:38,371 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:44:38,372 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:44:46,904 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:44:53,322 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:44:53,352 - text_processor - INFO - 成功处理实体: 科比台球纪念馆
2025-07-24 00:44:58,851 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:45:03,159 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:45:03,169 - text_processor - INFO - 成功处理实体: 贡吉嘎彩林卡
2025-07-24 00:45:08,906 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:45:14,707 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:45:14,718 - text_processor - INFO - 成功处理实体: 颂吉东阁大昭寺店
2025-07-24 00:45:14,719 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:45:14,719 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:45:14,820 - __main__ - INFO - 处理子批次 34/34
2025-07-24 00:45:14,820 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:45:14,821 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:45:23,491 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:45:29,638 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:45:29,686 - text_processor - INFO - 成功处理实体: 水木林卡
2025-07-24 00:45:29,686 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:45:29,687 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:45:29,788 - __main__ - INFO - 批次 batch_100_nodes 完成: 100/100 成功, 耗时: 1238.21秒
2025-07-24 00:45:29,789 - __main__ - INFO - 批次 100 结果:
2025-07-24 00:45:29,789 - __main__ - INFO -   - 成功: 100/100
2025-07-24 00:45:29,790 - __main__ - INFO -   - 耗时: 1238.21 秒
2025-07-24 00:45:29,790 - __main__ - INFO -   - 速度: 0.08 节点/秒
2025-07-24 00:45:31,794 - __main__ - INFO - 
==================================================
2025-07-24 00:45:31,794 - __main__ - INFO - 开始批次实验: 150 个节点
2025-07-24 00:45:31,794 - __main__ - INFO - ==================================================
2025-07-24 00:45:31,808 - neo4j_connection - INFO - 清空所有节点和关系
2025-07-24 00:45:31,811 - neo4j_connection - INFO - 清空所有节点和关系
2025-07-24 00:45:31,812 - text_processor - INFO - 清空所有节点和关系
2025-07-24 00:45:31,818 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX attraction_name IF NOT EXISTS FOR (e:Attraction) ON (e.name)` has no effect.} {description: `RANGE INDEX attraction_name FOR (e:Attraction) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)'
2025-07-24 00:45:31,822 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX city_name IF NOT EXISTS FOR (e:City) ON (e.name)` has no effect.} {description: `RANGE INDEX city_name FOR (e:City) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)'
2025-07-24 00:45:31,822 - text_processor - INFO - 创建 Attraction 和 City 名称索引
2025-07-24 00:45:31,823 - __main__ - INFO - 数据库已清空并重置
2025-07-24 00:45:31,823 - __main__ - INFO - 开始异步导入批次: batch_150_nodes, 节点数: 150
2025-07-24 00:45:31,823 - __main__ - INFO - 处理子批次 1/50
2025-07-24 00:45:31,824 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:45:31,824 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:45:37,911 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:45:37,924 - text_processor - INFO - 成功处理实体: 布达拉宫
2025-07-24 00:45:42,169 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:45:42,232 - text_processor - INFO - 成功处理实体: 大昭寺
2025-07-24 00:45:48,262 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:45:48,274 - text_processor - INFO - 成功处理实体: 纳木措
2025-07-24 00:45:48,275 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:45:53,602 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:45:54,967 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:46:00,589 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:46:00,731 - neo4j_crud - ERROR - Cypher 语法错误: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input 'RELATION': expected a parameter, '&', '*', ':', 'WHERE', ']', '{' or '|' (line 4, column 34 (offset: 152))

"            MERGE (source)-[r:NO RELATION]->(target)"

                                  ^}, 查询: 
            MATCH (source:Attraction {name: $source_name})
            MATCH (target:Attraction {name: $target_name})
            MERGE (source)-[r:NO RELATION]->(target)
            SET r += $props
            RETURN r
            , 参数: 布达拉宫 -> 纳木措
2025-07-24 00:46:00,734 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:46:00,840 - __main__ - INFO - 处理子批次 2/50
2025-07-24 00:46:00,841 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:46:00,841 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:46:05,452 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:46:05,528 - text_processor - INFO - 成功处理实体: 色拉寺
2025-07-24 00:46:09,636 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:46:09,648 - text_processor - INFO - 成功处理实体: 八廓街
2025-07-24 00:46:16,994 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:46:26,411 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:46:26,459 - text_processor - INFO - 成功处理实体: 《文成公主》大型史诗剧
2025-07-24 00:46:26,459 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:46:30,117 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:46:37,484 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:46:38,543 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:46:38,589 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:46:38,704 - __main__ - INFO - 处理子批次 3/50
2025-07-24 00:46:38,704 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:46:38,705 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:46:46,248 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:46:46,262 - text_processor - INFO - 成功处理实体: 罗布林卡
2025-07-24 00:46:53,700 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:46:53,710 - text_processor - INFO - 成功处理实体: 药王山
2025-07-24 00:46:59,605 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:46:59,627 - text_processor - INFO - 成功处理实体: 宗角禄康公园
2025-07-24 00:46:59,627 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:47:05,887 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:47:07,252 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:47:10,586 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:47:10,637 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:47:10,743 - __main__ - INFO - 处理子批次 4/50
2025-07-24 00:47:10,744 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:47:10,744 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:47:14,917 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:47:14,955 - text_processor - INFO - 成功处理实体: 哲蚌寺
2025-07-24 00:47:21,567 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:47:21,589 - text_processor - INFO - 成功处理实体: 小昭寺
2025-07-24 00:47:25,827 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:47:25,866 - text_processor - INFO - 成功处理实体: 西藏博物馆
2025-07-24 00:47:25,866 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:47:30,747 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:47:36,560 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:47:37,835 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:47:37,938 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:47:38,046 - __main__ - INFO - 处理子批次 5/50
2025-07-24 00:47:38,046 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:47:38,047 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:47:42,878 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:47:48,588 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:47:48,628 - text_processor - INFO - 成功处理实体: 娘热民俗风情园
2025-07-24 00:47:52,728 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:47:52,743 - text_processor - INFO - 成功处理实体: 仓姑寺
2025-07-24 00:48:02,686 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:48:02,700 - text_processor - INFO - 成功处理实体: 扎基寺
2025-07-24 00:48:02,700 - text_processor - INFO - 过滤后景点数量: 2
2025-07-24 00:48:11,777 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:48:11,819 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:48:11,932 - __main__ - INFO - 处理子批次 6/50
2025-07-24 00:48:11,933 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:48:11,933 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:48:18,824 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:48:18,869 - text_processor - INFO - 成功处理实体: 西藏拉萨清真大寺
2025-07-24 00:48:23,928 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:48:23,938 - text_processor - INFO - 成功处理实体: 拉萨河
2025-07-24 00:48:28,297 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:48:28,316 - text_processor - INFO - 成功处理实体: 帕邦喀
2025-07-24 00:48:28,316 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:48:35,619 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:48:35,650 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:48:37,109 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:48:37,138 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:48:37,248 - __main__ - INFO - 处理子批次 7/50
2025-07-24 00:48:37,248 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:48:37,248 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:48:42,084 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:48:42,159 - text_processor - INFO - 成功处理实体: 拉鲁湿地国家级自然保护区
2025-07-24 00:48:46,814 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:48:46,832 - text_processor - INFO - 成功处理实体: 喜德寺
2025-07-24 00:48:54,890 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:48:54,909 - text_processor - INFO - 成功处理实体: 羊八井
2025-07-24 00:48:54,909 - text_processor - INFO - 过滤后景点数量: 2
2025-07-24 00:49:00,559 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:49:00,596 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:49:00,702 - __main__ - INFO - 处理子批次 8/50
2025-07-24 00:49:00,702 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:49:00,702 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:49:06,169 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:49:14,771 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:49:14,796 - text_processor - INFO - 成功处理实体: 直贡噶举派寺庙群
2025-07-24 00:49:19,957 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:49:19,969 - text_processor - INFO - 成功处理实体: 甘丹寺
2025-07-24 00:49:26,892 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:49:26,913 - text_processor - INFO - 成功处理实体: 米拉山口
2025-07-24 00:49:26,913 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:49:35,737 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:49:37,669 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:49:39,132 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:49:39,179 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:49:39,285 - __main__ - INFO - 处理子批次 9/50
2025-07-24 00:49:39,285 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:49:39,286 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:49:45,259 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:49:45,284 - text_processor - INFO - 成功处理实体: 和平解放纪念碑
2025-07-24 00:49:51,254 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:49:51,268 - text_processor - INFO - 成功处理实体: 布达拉宫广场
2025-07-24 00:49:54,364 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:49:54,385 - text_processor - INFO - 成功处理实体: 那根拉山口
2025-07-24 00:49:54,386 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:50:00,940 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:50:01,910 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:50:05,679 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:50:05,719 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:50:05,828 - __main__ - INFO - 处理子批次 10/50
2025-07-24 00:50:05,828 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:50:05,828 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:50:11,880 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:50:11,917 - text_processor - INFO - 成功处理实体: 木如寺印经院
2025-07-24 00:50:17,782 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:50:17,792 - text_processor - INFO - 成功处理实体: 下密寺
2025-07-24 00:50:25,346 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:50:25,368 - text_processor - INFO - 成功处理实体: 驻藏大臣衙门遗址
2025-07-24 00:50:25,368 - text_processor - INFO - 过滤后景点数量: 3
2025-07-24 00:50:29,055 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:50:32,283 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:50:37,190 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:50:37,242 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:50:37,347 - __main__ - INFO - 处理子批次 11/50
2025-07-24 00:50:37,347 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:50:37,347 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:50:42,350 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:50:42,384 - text_processor - INFO - 成功处理实体: 小昭寺路
2025-07-24 00:50:50,030 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:50:57,026 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:50:57,047 - text_processor - INFO - 成功处理实体: 拉萨民族文化艺术宫
2025-07-24 00:51:01,149 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:51:07,874 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:51:07,893 - text_processor - INFO - 成功处理实体: 5238
2025-07-24 00:51:07,894 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:51:07,894 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:51:07,999 - __main__ - INFO - 处理子批次 12/50
2025-07-24 00:51:07,999 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:51:07,999 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:51:12,851 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:51:24,552 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:51:24,590 - text_processor - INFO - 成功处理实体: 纳木措扎西岛
2025-07-24 00:51:38,754 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:51:44,692 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:51:44,705 - text_processor - INFO - 成功处理实体: 西藏藏医药文化博览中心
2025-07-24 00:51:48,781 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:51:48,800 - text_processor - INFO - 成功处理实体: 达扎路恭纪功碑
2025-07-24 00:51:48,800 - text_processor - INFO - 过滤后景点数量: 1
2025-07-24 00:51:48,801 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:51:48,905 - __main__ - INFO - 处理子批次 13/50
2025-07-24 00:51:48,905 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:51:48,905 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:51:53,219 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:52:03,291 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:52:03,324 - text_processor - INFO - 成功处理实体: 甲玛王宫
2025-07-24 00:52:12,540 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:52:12,564 - text_processor - INFO - 成功处理实体: 扎希寺
2025-07-24 00:52:17,298 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:52:17,337 - text_processor - INFO - 成功处理实体: 楚布寺
2025-07-24 00:52:17,338 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:52:17,338 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:52:17,449 - __main__ - INFO - 处理子批次 14/50
2025-07-24 00:52:17,449 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:52:17,451 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:52:22,297 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:52:22,330 - text_processor - INFO - 成功处理实体: 松赞干布出生地
2025-07-24 00:52:28,099 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:52:28,113 - text_processor - INFO - 成功处理实体: 聂当度母殿和聂当大佛
2025-07-24 00:52:36,407 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:52:36,419 - text_processor - INFO - 成功处理实体: 策门林寺
2025-07-24 00:52:36,420 - text_processor - INFO - 过滤后景点数量: 1
2025-07-24 00:52:36,420 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:52:36,529 - __main__ - INFO - 处理子批次 15/50
2025-07-24 00:52:36,529 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:52:36,530 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:52:42,646 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:52:42,694 - text_processor - INFO - 成功处理实体: 鲁普岩寺
2025-07-24 00:52:48,370 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:52:48,389 - text_processor - INFO - 成功处理实体: 仙足岛
2025-07-24 00:52:58,171 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:52:58,192 - text_processor - INFO - 成功处理实体: 曲贡古遗址
2025-07-24 00:52:58,192 - text_processor - INFO - 过滤后景点数量: 1
2025-07-24 00:52:58,193 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:52:58,300 - __main__ - INFO - 处理子批次 16/50
2025-07-24 00:52:58,300 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:52:58,300 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:53:07,427 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:53:07,460 - text_processor - INFO - 成功处理实体: 乃琼寺
2025-07-24 00:53:16,162 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:53:16,174 - text_processor - INFO - 成功处理实体: 次巴拉康寺
2025-07-24 00:53:26,223 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:53:26,246 - text_processor - INFO - 成功处理实体: 清政府驻藏大臣衙门旧址
2025-07-24 00:53:26,246 - text_processor - INFO - 过滤后景点数量: 1
2025-07-24 00:53:26,247 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:53:26,363 - __main__ - INFO - 处理子批次 17/50
2025-07-24 00:53:26,364 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:53:26,364 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:53:34,258 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:53:50,789 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:53:50,825 - text_processor - INFO - 成功处理实体: 热振寺
2025-07-24 00:53:59,806 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:53:59,825 - text_processor - INFO - 成功处理实体: 关帝庙
2025-07-24 00:54:07,237 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:54:25,078 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:54:25,089 - text_processor - INFO - 成功处理实体: 喜德林
2025-07-24 00:54:25,091 - text_processor - INFO - 过滤后景点数量: 0
2025-07-24 00:54:25,091 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-24 00:54:25,200 - __main__ - INFO - 处理子批次 18/50
2025-07-24 00:54:25,200 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-24 00:54:25,200 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-24 00:54:39,709 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:54:52,665 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-24 00:54:52,696 - text_processor - INFO - 成功处理实体: 白色寺
2025-07-24 00:55:00,262 - httpx - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
