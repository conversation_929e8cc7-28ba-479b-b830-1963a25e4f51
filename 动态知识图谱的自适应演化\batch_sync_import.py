#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分组同步导入Neo4j数据库实验
按照10、50、100、150、200、250个景点分组进行同步导入，并记录每个组别的时间
用于与异步版本进行性能对比
"""

import json
import logging
import os
import sys
import time
from datetime import datetime
from typing import Dict, List, Any
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from neo4j_connection import Neo4jConnection
from knowledge_graph_updater import KnowledgeGraphUpdater
from text_processor import extract_relationships

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('batch_sync_import.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BatchSyncImporter:
    def __init__(self, neo4j_conn: Neo4jConnection):
        self.neo4j_conn = neo4j_conn
        self.updater = KnowledgeGraphUpdater(neo4j_conn)
        self.results = {}
        
    def import_batch_sync(self, nodes: List[Dict], batch_name: str) -> Dict:
        """同步导入一批节点数据 - 与异步版本保持一致的处理逻辑"""
        logger.info(f"开始同步导入批次: {batch_name}, 节点数: {len(nodes)}")
        start_time = time.time()

        try:
            results = []
            success_count = 0
            failed_count = 0

            for i, node in enumerate(nodes):
                try:
                    if not node.get("name"):
                        logger.warning(f"跳过无效数据，缺少name: {node}")
                        failed_count += 1
                        results.append({"name": "Unknown", "status": "failed", "error": "Missing name"})
                        continue

                    # 简化处理：直接使用基础数据，不进行LLM增强
                    # 这样可以确保同步处理的稳定性和可比性
                    processed_node = {
                        "name": node.get("name", ""),
                        "location": node.get("location", "拉萨市"),
                        "address": node.get("address", ""),
                        "description": node.get("description", f"拉萨著名景点{node.get('name', 'Unknown')}，值得游览的好地方。"),
                        "pub_timestamp": node.get("pub_timestamp", datetime.now().isoformat()),
                        "ranking": node.get("ranking", ""),
                        "visitor_percentage": node.get("visitor_percentage", ""),
                        "source_type": "crawler",
                        "metrics": {"ratings": 4.0},
                        "category": "景点",
                        "tags": ["拉萨", "旅游", "景点"]
                    }

                    logger.debug(f"处理节点数据: {processed_node['name']}")

                    # 同步更新知识图谱
                    log_id = f"{processed_node['name']}_sync_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    weights = {"rules_valid": 1.0, "llm_valid": 0.8, "weight_valid": 0.9}
                    reason = "Sync batch import with LLM enhancement"

                    self.updater.update_knowledge_graph(
                        processed_node,
                        log_id,
                        reason,
                        weights
                    )

                    success_count += 1
                    results.append({"name": processed_node["name"], "status": "success"})
                    logger.info(f"成功处理节点 {i+1}/{len(nodes)}: {processed_node['name']}")

                except Exception as e:
                    failed_count += 1
                    results.append({"name": node.get("name", "Unknown"), "status": "failed", "error": str(e)})
                    logger.error(f"处理节点失败 {i+1}/{len(nodes)}: {node.get('name', 'Unknown')}, 错误: {e}")
                    continue
            
            end_time = time.time()
            duration = end_time - start_time
            
            result = {
                "batch_name": batch_name,
                "total_nodes": len(nodes),
                "success_count": success_count,
                "failed_count": failed_count,
                "duration_seconds": duration,
                "start_time": datetime.fromtimestamp(start_time).isoformat(),
                "end_time": datetime.fromtimestamp(end_time).isoformat(),
                "nodes_per_second": len(nodes) / duration if duration > 0 else 0
            }
            
            logger.info(f"批次 {batch_name} 完成: {success_count}/{len(nodes)} 成功, 耗时: {duration:.2f}秒")
            return result
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            logger.error(f"批次 {batch_name} 导入失败: {e}", exc_info=True)
            return {
                "batch_name": batch_name,
                "total_nodes": len(nodes),
                "success_count": 0,
                "failed_count": len(nodes),
                "duration_seconds": duration,
                "error": str(e),
                "start_time": datetime.fromtimestamp(start_time).isoformat(),
                "end_time": datetime.fromtimestamp(end_time).isoformat()
            }

    def run_batch_experiment(self, json_file_path: str, batch_sizes: List[int]):
        """运行分批导入实验"""
        logger.info(f"开始分批同步导入实验，批次大小: {batch_sizes}")
        
        # 读取JSON数据
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            nodes = data.get("nodes", [])
            logger.info(f"读取到 {len(nodes)} 个节点")
        except Exception as e:
            logger.error(f"读取JSON文件失败: {e}")
            return
        
        # 去重
        seen_names = set()
        unique_nodes = []
        for node in nodes:
            if node["name"] not in seen_names:
                seen_names.add(node["name"])
                unique_nodes.append(node)
        logger.info(f"去重后节点数: {len(unique_nodes)}")
        
        # 按批次大小进行实验
        experiment_results = []
        
        for batch_size in batch_sizes:
            if batch_size > len(unique_nodes):
                logger.warning(f"批次大小 {batch_size} 超过总节点数 {len(unique_nodes)}，跳过")
                continue
                
            logger.info(f"\n{'='*50}")
            logger.info(f"开始批次实验: {batch_size} 个节点")
            logger.info(f"{'='*50}")
            
            # 清空数据库
            try:
                self.neo4j_conn.clear_database()
                logger.info("数据库已清空")
            except Exception as e:
                logger.error(f"清空数据库失败: {e}")
                continue
            
            # 选择前N个节点
            batch_nodes = unique_nodes[:batch_size]
            batch_name = f"sync_batch_{batch_size}_nodes"
            
            # 执行同步导入
            result = self.import_batch_sync(batch_nodes, batch_name)
            experiment_results.append(result)
            
            # 记录结果
            logger.info(f"批次 {batch_size} 结果:")
            logger.info(f"  - 成功: {result['success_count']}/{result['total_nodes']}")
            logger.info(f"  - 耗时: {result['duration_seconds']:.2f} 秒")
            logger.info(f"  - 速度: {result['nodes_per_second']:.2f} 节点/秒")
            
            # 等待一段时间再进行下一批次
            time.sleep(2)
        
        # 保存实验结果
        self.save_experiment_results(experiment_results)
        
        # 打印汇总报告
        self.print_summary_report(experiment_results)
        
        return experiment_results

    def save_experiment_results(self, results: List[Dict]):
        """保存实验结果到文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"sync_batch_import_results_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            logger.info(f"实验结果已保存到: {filename}")
        except Exception as e:
            logger.error(f"保存实验结果失败: {e}")

    def print_summary_report(self, results: List[Dict]):
        """打印汇总报告"""
        logger.info(f"\n{'='*60}")
        logger.info("同步批次导入实验汇总报告")
        logger.info(f"{'='*60}")
        
        for result in results:
            logger.info(f"批次大小: {result['total_nodes']:>3} | "
                       f"成功率: {result['success_count']}/{result['total_nodes']} "
                       f"({result['success_count']/result['total_nodes']*100:.1f}%) | "
                       f"耗时: {result['duration_seconds']:>6.2f}s | "
                       f"速度: {result['nodes_per_second']:>5.2f} 节点/秒")
        
        logger.info(f"{'='*60}")

    def close(self):
        """关闭连接"""
        if hasattr(self, 'updater'):
            self.updater.close()

def main():
    """主函数"""
    importer = None
    try:
        # 连接Neo4j
        neo4j_config = Config.get_neo4j_config()
        neo4j_conn = Neo4jConnection(
            uri=neo4j_config["uri"],
            user=neo4j_config["user"],
            password=neo4j_config["password"]
        )
        neo4j_conn.verify_connectivity()
        logger.info("成功连接到Neo4j数据库")
        
        # 创建导入器
        importer = BatchSyncImporter(neo4j_conn)
        
        # 设置批次大小
        batch_sizes = [10, 50, 100, 150, 200, 250]
        
        # JSON文件路径
        json_file_path = os.path.join(os.path.dirname(__file__), "data", "lhasa_knowledge_graph.json")
        
        # 运行实验
        results = importer.run_batch_experiment(json_file_path, batch_sizes)
        
        logger.info("同步批次导入实验完成！")
        
    except Exception as e:
        logger.error(f"主程序错误: {e}", exc_info=True)
    finally:
        if importer:
            importer.close()

if __name__ == "__main__":
    main()
