#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成异步vs同步处理能力对比图表
基于实际异步实验数据和预估同步数据
"""

import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime

# 配置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def create_comparison_charts():
    """创建对比图表"""
    
    # 实际异步实验数据
    batch_sizes = [10, 50, 100, 150, 200, 250]
    
    # 异步处理时间 (秒) - 基于实际实验数据的平均值
    async_times = [110.69, 513.75, 1324.55, 3273.08, 4422.49, 5882.45]
    
    # 同步处理时间 (秒) - 基于异步数据预估 (假设同步比异步慢35%)
    sync_times = [t * 1.35 for t in async_times]
    
    # 处理速度 (节点/秒)
    async_speeds = [size/time for size, time in zip(batch_sizes, async_times)]
    sync_speeds = [size/time for size, time in zip(batch_sizes, sync_times)]
    
    # 创建2x2子图
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 处理时间对比 (柱状图)
    x = np.arange(len(batch_sizes))
    width = 0.35
    
    bars1 = ax1.bar(x - width/2, async_times, width, label='异步处理', color='#5B9BD5', alpha=0.8)
    bars2 = ax1.bar(x + width/2, sync_times, width, label='同步处理 (预估)', color='#FF6B6B', alpha=0.8)
    
    ax1.set_xlabel('批次大小 (个景点)')
    ax1.set_ylabel('处理时间 (秒)')
    ax1.set_title('同步 vs 异步处理时间对比 (秒)')
    ax1.set_xticks(x)
    ax1.set_xticklabels(batch_sizes)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 在柱子上添加数值标签
    for bar in bars1:
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{int(height)}s', ha='center', va='bottom', fontsize=9)
    
    # 2. 处理速度对比 (折线图)
    ax2.plot(batch_sizes, async_speeds, 'o-', label='异步处理', linewidth=2, markersize=6, color='#5B9BD5')
    ax2.plot(batch_sizes, sync_speeds, 's-', label='同步处理 (预估)', linewidth=2, markersize=6, color='#FF6B6B')
    ax2.set_xlabel('批次大小 (个景点)')
    ax2.set_ylabel('处理速度 (节点/秒)')
    ax2.set_title('处理速度对比')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(0, max(async_speeds) * 1.1)
    
    # 3. 累积处理时间 (折线图)
    cumulative_async = np.cumsum(async_times)
    cumulative_sync = np.cumsum(sync_times)
    
    ax3.plot(batch_sizes, cumulative_async, 'o-', label='异步处理', linewidth=2, markersize=6, color='#5B9BD5')
    ax3.plot(batch_sizes, cumulative_sync, 's-', label='同步处理 (预估)', linewidth=2, markersize=6, color='#FF6B6B')
    ax3.set_xlabel('批次大小 (个景点)')
    ax3.set_ylabel('累积时间 (秒)')
    ax3.set_title('累积处理时间对比')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 性能提升倍数 (柱状图)
    improvement_ratios = [(s-a)/s*100 for a, s in zip(async_times, sync_times)]
    
    bars = ax4.bar(batch_sizes, improvement_ratios, color='#70AD47', alpha=0.8, width=15)
    ax4.set_xlabel('批次大小 (个景点)')
    ax4.set_ylabel('性能提升百分比 (%)')
    ax4.set_title('异步相对于同步的性能提升')
    ax4.grid(True, alpha=0.3)
    ax4.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    
    # 在柱子上添加百分比标签
    for bar, ratio in zip(bars, improvement_ratios):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{ratio:.1f}%', ha='center', va='bottom', fontsize=10, fontweight='bold')
    
    plt.tight_layout()
    
    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"async_vs_sync_comparison_{timestamp}.png"
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"对比图表已保存: {filename}")
    
    # 不显示图表，只保存
    # plt.show()
    
    return filename

def print_summary_table():
    """打印汇总对比表格"""
    print("\n" + "="*80)
    print("异步 vs 同步处理性能对比汇总表")
    print("="*80)
    
    batch_sizes = [10, 50, 100, 150, 200, 250]
    async_times = [110.69, 513.75, 1324.55, 3273.08, 4422.49, 5882.45]
    sync_times = [t * 1.35 for t in async_times]
    async_speeds = [size/time for size, time in zip(batch_sizes, async_times)]
    sync_speeds = [size/time for size, time in zip(batch_sizes, sync_times)]
    improvements = [(s-a)/s*100 for a, s in zip(async_times, sync_times)]
    
    print(f"{'批次大小':<8} {'异步时间':<10} {'同步时间':<10} {'异步速度':<10} {'同步速度':<10} {'性能提升':<10}")
    print("-" * 80)
    
    for i, size in enumerate(batch_sizes):
        print(f"{size:<8} "
              f"{async_times[i]:<10.1f} "
              f"{sync_times[i]:<10.1f} "
              f"{async_speeds[i]:<10.3f} "
              f"{sync_speeds[i]:<10.3f} "
              f"{improvements[i]:<10.1f}%")
    
    print("-" * 80)
    avg_improvement = sum(improvements) / len(improvements)
    print(f"平均性能提升: {avg_improvement:.1f}%")
    print("="*80)

def main():
    """主函数"""
    print("生成异步vs同步处理能力对比图表...")
    
    # 生成图表
    filename = create_comparison_charts()
    
    # 打印汇总表格
    print_summary_table()
    
    print(f"\n实验数据基于:")
    print("- 异步数据: 实际实验测量结果")
    print("- 同步数据: 基于异步数据预估 (假设同步比异步慢35%)")
    print("- 数据来源: Neo4j知识图谱异步导入实验")
    print("- 实验日期: 2025年7月23-24日")

if __name__ == "__main__":
    main()
