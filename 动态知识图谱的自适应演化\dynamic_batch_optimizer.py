#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态批次大小优化器 - 根据实时性能调整批次大小
"""

import asyncio
import json
import logging
import time
import statistics
from typing import Dict, List, Any, Tuple
from dataclasses import dataclass
from collections import deque

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """性能指标"""
    batch_size: int
    processing_time: float
    success_rate: float
    nodes_per_second: float
    api_error_rate: float
    memory_usage: float = 0.0
    
    @property
    def efficiency_score(self) -> float:
        """综合效率评分"""
        # 综合考虑速度、成功率、错误率
        speed_score = min(self.nodes_per_second / 0.2, 1.0)  # 0.2节点/秒为满分
        success_score = self.success_rate
        error_penalty = max(0, 1 - self.api_error_rate * 2)  # 错误率惩罚
        
        return (speed_score * 0.4 + success_score * 0.4 + error_penalty * 0.2)


class DynamicBatchOptimizer:
    """动态批次大小优化器"""
    
    def __init__(self, initial_batch_size: int = 20, min_batch_size: int = 5, max_batch_size: int = 50):
        self.current_batch_size = initial_batch_size
        self.min_batch_size = min_batch_size
        self.max_batch_size = max_batch_size
        
        # 性能历史记录
        self.performance_history = deque(maxlen=10)  # 保留最近10次的性能数据
        self.batch_size_history = deque(maxlen=20)   # 保留最近20次的批次大小
        
        # 优化参数
        self.adjustment_factor = 0.2  # 调整幅度
        self.stability_threshold = 3  # 稳定性阈值
        self.improvement_threshold = 0.05  # 改进阈值(5%)
        
        # 状态跟踪
        self.consecutive_improvements = 0
        self.consecutive_degradations = 0
        self.last_adjustment_time = time.time()
        
    def record_performance(self, metrics: PerformanceMetrics):
        """记录性能指标"""
        self.performance_history.append(metrics)
        self.batch_size_history.append(metrics.batch_size)
        
        logger.debug(f"记录性能: 批次大小={metrics.batch_size}, "
                    f"速度={metrics.nodes_per_second:.3f}, "
                    f"成功率={metrics.success_rate:.3f}, "
                    f"效率评分={metrics.efficiency_score:.3f}")
    
    def should_adjust_batch_size(self) -> bool:
        """判断是否应该调整批次大小"""
        if len(self.performance_history) < 2:
            return False
        
        # 至少间隔30秒才调整
        if time.time() - self.last_adjustment_time < 30:
            return False
        
        # 检查性能趋势
        recent_scores = [m.efficiency_score for m in list(self.performance_history)[-3:]]
        if len(recent_scores) < 2:
            return False
        
        # 如果性能持续下降，需要调整
        if all(recent_scores[i] > recent_scores[i+1] for i in range(len(recent_scores)-1)):
            self.consecutive_degradations += 1
            return self.consecutive_degradations >= 2
        
        # 如果性能稳定但不是最优，尝试优化
        if len(self.performance_history) >= 5:
            avg_score = statistics.mean(m.efficiency_score for m in self.performance_history)
            if avg_score < 0.8:  # 效率评分低于80%
                return True
        
        return False
    
    def calculate_optimal_batch_size(self) -> int:
        """计算最优批次大小"""
        if len(self.performance_history) < 2:
            return self.current_batch_size
        
        # 分析性能趋势
        recent_metrics = list(self.performance_history)[-5:]  # 最近5次
        
        # 找到效率最高的批次大小
        best_metric = max(recent_metrics, key=lambda m: m.efficiency_score)
        best_batch_size = best_metric.batch_size
        
        # 分析批次大小与性能的关系
        if len(recent_metrics) >= 3:
            # 计算性能梯度
            batch_sizes = [m.batch_size for m in recent_metrics]
            efficiency_scores = [m.efficiency_score for m in recent_metrics]
            
            # 简单的梯度分析
            if len(set(batch_sizes)) > 1:  # 确保有不同的批次大小
                correlation = self._calculate_correlation(batch_sizes, efficiency_scores)
                
                if correlation > 0.3:  # 正相关，增大批次
                    new_size = min(self.max_batch_size, 
                                 int(self.current_batch_size * (1 + self.adjustment_factor)))
                elif correlation < -0.3:  # 负相关，减小批次
                    new_size = max(self.min_batch_size, 
                                 int(self.current_batch_size * (1 - self.adjustment_factor)))
                else:  # 相关性不强，使用历史最优
                    new_size = best_batch_size
            else:
                new_size = best_batch_size
        else:
            new_size = best_batch_size
        
        # 避免频繁小幅调整
        if abs(new_size - self.current_batch_size) < 3:
            return self.current_batch_size
        
        return new_size
    
    def _calculate_correlation(self, x: List[float], y: List[float]) -> float:
        """计算简单的相关系数"""
        if len(x) != len(y) or len(x) < 2:
            return 0.0
        
        n = len(x)
        sum_x = sum(x)
        sum_y = sum(y)
        sum_xy = sum(x[i] * y[i] for i in range(n))
        sum_x2 = sum(x[i] ** 2 for i in range(n))
        sum_y2 = sum(y[i] ** 2 for i in range(n))
        
        denominator = ((n * sum_x2 - sum_x ** 2) * (n * sum_y2 - sum_y ** 2)) ** 0.5
        if denominator == 0:
            return 0.0
        
        correlation = (n * sum_xy - sum_x * sum_y) / denominator
        return correlation
    
    def adjust_batch_size(self) -> Tuple[int, str]:
        """调整批次大小"""
        if not self.should_adjust_batch_size():
            return self.current_batch_size, "无需调整"
        
        old_size = self.current_batch_size
        new_size = self.calculate_optimal_batch_size()
        
        if new_size != old_size:
            self.current_batch_size = new_size
            self.last_adjustment_time = time.time()
            
            if new_size > old_size:
                reason = f"性能分析显示可以增大批次大小以提升效率"
            else:
                reason = f"性能分析显示需要减小批次大小以避免瓶颈"
            
            logger.info(f"动态调整批次大小: {old_size} → {new_size} ({reason})")
            return new_size, reason
        
        return self.current_batch_size, "保持当前批次大小"
    
    def get_recommended_sub_batch_size(self, total_nodes: int) -> int:
        """获取推荐的子批次大小"""
        # 基于当前批次大小和总节点数计算子批次大小
        if total_nodes <= self.current_batch_size:
            return total_nodes
        
        # 确保子批次数量合理（不要太多也不要太少）
        ideal_sub_batches = max(2, min(8, total_nodes // self.current_batch_size))
        sub_batch_size = total_nodes // ideal_sub_batches
        
        # 调整到合理范围
        sub_batch_size = max(self.min_batch_size, min(self.max_batch_size, sub_batch_size))
        
        return sub_batch_size
    
    def get_optimization_summary(self) -> Dict[str, Any]:
        """获取优化总结"""
        if not self.performance_history:
            return {"status": "无性能数据"}
        
        recent_metrics = list(self.performance_history)[-5:]
        avg_efficiency = statistics.mean(m.efficiency_score for m in recent_metrics)
        avg_speed = statistics.mean(m.nodes_per_second for m in recent_metrics)
        avg_success_rate = statistics.mean(m.success_rate for m in recent_metrics)
        
        batch_size_range = (min(self.batch_size_history), max(self.batch_size_history))
        
        return {
            "current_batch_size": self.current_batch_size,
            "batch_size_range": batch_size_range,
            "avg_efficiency_score": avg_efficiency,
            "avg_processing_speed": avg_speed,
            "avg_success_rate": avg_success_rate,
            "total_adjustments": len(set(self.batch_size_history)),
            "performance_trend": self._get_performance_trend()
        }
    
    def _get_performance_trend(self) -> str:
        """获取性能趋势"""
        if len(self.performance_history) < 3:
            return "数据不足"
        
        recent_scores = [m.efficiency_score for m in list(self.performance_history)[-3:]]
        
        if recent_scores[-1] > recent_scores[0] + 0.05:
            return "改善中"
        elif recent_scores[-1] < recent_scores[0] - 0.05:
            return "下降中"
        else:
            return "稳定"


class AdaptiveBatchProcessor:
    """自适应批处理器"""
    
    def __init__(self, processor_func, initial_batch_size: int = 20):
        self.processor_func = processor_func
        self.optimizer = DynamicBatchOptimizer(initial_batch_size)
        self.total_processed = 0
        self.total_time = 0.0
        
    async def process_with_adaptive_batching(self, nodes: List[Dict], batch_name: str) -> Dict:
        """使用自适应批处理处理节点"""
        total_nodes = len(nodes)
        logger.info(f"开始自适应批处理: {batch_name}, 总节点数: {total_nodes}")
        
        start_time = time.time()
        all_results = []
        batch_count = 0
        
        # 按当前最优批次大小分批处理
        current_pos = 0
        while current_pos < total_nodes:
            batch_count += 1
            
            # 获取当前批次大小
            current_batch_size = self.optimizer.current_batch_size
            batch_nodes = nodes[current_pos:current_pos + current_batch_size]
            actual_batch_size = len(batch_nodes)
            
            logger.info(f"处理批次 {batch_count}: {actual_batch_size} 个节点 (批次大小: {current_batch_size})")
            
            # 处理当前批次
            batch_start_time = time.time()
            try:
                batch_result = await self.processor_func(batch_nodes, f"{batch_name}_batch_{batch_count}")
                batch_end_time = time.time()
                batch_duration = batch_end_time - batch_start_time
                
                # 计算性能指标
                success_count = batch_result.get('success_count', 0)
                success_rate = success_count / actual_batch_size if actual_batch_size > 0 else 0
                nodes_per_second = actual_batch_size / batch_duration if batch_duration > 0 else 0
                api_error_rate = batch_result.get('failed_count', 0) / actual_batch_size if actual_batch_size > 0 else 0
                
                # 记录性能
                metrics = PerformanceMetrics(
                    batch_size=actual_batch_size,
                    processing_time=batch_duration,
                    success_rate=success_rate,
                    nodes_per_second=nodes_per_second,
                    api_error_rate=api_error_rate
                )
                
                self.optimizer.record_performance(metrics)
                all_results.extend(batch_result.get('results', []))
                
                logger.info(f"批次 {batch_count} 完成: {success_count}/{actual_batch_size} 成功, "
                           f"耗时: {batch_duration:.2f}秒, 速度: {nodes_per_second:.3f}节点/秒")
                
            except Exception as e:
                logger.error(f"批次 {batch_count} 处理失败: {e}")
                # 记录失败的性能指标
                metrics = PerformanceMetrics(
                    batch_size=actual_batch_size,
                    processing_time=time.time() - batch_start_time,
                    success_rate=0.0,
                    nodes_per_second=0.0,
                    api_error_rate=1.0
                )
                self.optimizer.record_performance(metrics)
            
            current_pos += actual_batch_size
            
            # 动态调整批次大小
            if current_pos < total_nodes:  # 还有剩余节点
                new_batch_size, adjustment_reason = self.optimizer.adjust_batch_size()
                if new_batch_size != current_batch_size:
                    logger.info(f"批次大小调整: {current_batch_size} → {new_batch_size} ({adjustment_reason})")
            
            # 批次间短暂休息
            await asyncio.sleep(1)
        
        # 汇总结果
        end_time = time.time()
        total_duration = end_time - start_time
        
        success_count = sum(1 for r in all_results if r.get('status') == 'success')
        failed_count = len(all_results) - success_count
        
        result = {
            "batch_name": batch_name,
            "total_nodes": total_nodes,
            "success_count": success_count,
            "failed_count": failed_count,
            "duration_seconds": total_duration,
            "nodes_per_second": total_nodes / total_duration if total_duration > 0 else 0,
            "batch_count": batch_count,
            "optimization_summary": self.optimizer.get_optimization_summary(),
            "processing_type": "adaptive_batching"
        }
        
        logger.info(f"自适应批处理完成: {batch_name}")
        logger.info(f"  总批次数: {batch_count}")
        logger.info(f"  成功率: {success_count}/{total_nodes} ({success_count/total_nodes*100:.1f}%)")
        logger.info(f"  总耗时: {total_duration:.2f}秒")
        logger.info(f"  平均速度: {result['nodes_per_second']:.3f}节点/秒")
        
        return result
