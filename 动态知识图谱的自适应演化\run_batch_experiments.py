#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批次导入实验运行脚本
自动运行异步和同步导入实验，并生成性能对比报告
"""

import asyncio
import logging
import os
import sys
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from batch_async_import import BatchAsyncImporter
from batch_sync_import import BatchSyncImporter
from performance_comparison import PerformanceComparator
from config import Config
from neo4j_connection import Neo4jConnection

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('batch_experiments.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BatchExperimentRunner:
    def __init__(self):
        self.neo4j_conn = None
        self.batch_sizes = [10, 50, 100, 150, 200, 250]
        self.json_file_path = os.path.join(os.path.dirname(__file__), "data", "lhasa_knowledge_graph.json")
        
    def setup_connection(self):
        """设置Neo4j连接"""
        try:
            neo4j_config = Config.get_neo4j_config()
            self.neo4j_conn = Neo4jConnection(
                uri=neo4j_config["uri"],
                user=neo4j_config["user"],
                password=neo4j_config["password"]
            )
            self.neo4j_conn.verify_connectivity()
            logger.info("成功连接到Neo4j数据库")
            return True
        except Exception as e:
            logger.error(f"连接Neo4j失败: {e}")
            return False
            
    async def run_async_experiment(self):
        """运行异步导入实验"""
        logger.info("\n" + "="*60)
        logger.info("开始异步导入实验")
        logger.info("="*60)
        
        try:
            importer = BatchAsyncImporter(self.neo4j_conn)
            results = await importer.run_batch_experiment(self.json_file_path, self.batch_sizes)
            importer.close()
            logger.info("异步导入实验完成")
            return results
        except Exception as e:
            logger.error(f"异步导入实验失败: {e}")
            return None
            
    def run_sync_experiment(self):
        """运行同步导入实验"""
        logger.info("\n" + "="*60)
        logger.info("开始同步导入实验")
        logger.info("="*60)
        
        try:
            importer = BatchSyncImporter(self.neo4j_conn)
            results = importer.run_batch_experiment(self.json_file_path, self.batch_sizes)
            importer.close()
            logger.info("同步导入实验完成")
            return results
        except Exception as e:
            logger.error(f"同步导入实验失败: {e}")
            return None
            
    def generate_comparison_report(self):
        """生成性能对比报告"""
        logger.info("\n" + "="*60)
        logger.info("开始生成性能对比报告")
        logger.info("="*60)
        
        try:
            comparator = PerformanceComparator()
            comparator.load_results()
            comparison_data = comparator.generate_comparison_report()
            logger.info("性能对比报告生成完成")
            return comparison_data
        except Exception as e:
            logger.error(f"生成性能对比报告失败: {e}")
            return None
            
    async def run_all_experiments(self):
        """运行所有实验"""
        start_time = time.time()
        logger.info("开始批次导入性能对比实验")
        logger.info(f"实验批次大小: {self.batch_sizes}")
        logger.info(f"数据文件: {self.json_file_path}")
        
        # 设置连接
        if not self.setup_connection():
            logger.error("无法连接到Neo4j，实验终止")
            return
            
        try:
            # 1. 运行异步实验
            async_results = await self.run_async_experiment()
            if not async_results:
                logger.error("异步实验失败，跳过后续步骤")
                return
                
            # 等待一段时间
            logger.info("等待5秒后开始同步实验...")
            await asyncio.sleep(5)
            
            # 2. 运行同步实验
            sync_results = self.run_sync_experiment()
            if not sync_results:
                logger.error("同步实验失败，跳过对比报告生成")
                return
                
            # 等待一段时间
            logger.info("等待3秒后生成对比报告...")
            time.sleep(3)
            
            # 3. 生成对比报告
            comparison_data = self.generate_comparison_report()
            
            # 4. 打印最终总结
            end_time = time.time()
            total_duration = end_time - start_time
            
            logger.info("\n" + "="*80)
            logger.info("批次导入性能对比实验总结")
            logger.info("="*80)
            logger.info(f"总实验时间: {total_duration:.2f} 秒")
            logger.info(f"异步实验批次数: {len(async_results) if async_results else 0}")
            logger.info(f"同步实验批次数: {len(sync_results) if sync_results else 0}")
            
            if comparison_data:
                avg_time_improvement = sum(d['time_improvement_pct'] for d in comparison_data) / len(comparison_data)
                avg_speed_improvement = sum(d['speed_improvement_pct'] for d in comparison_data) / len(comparison_data)
                logger.info(f"平均时间提升: {avg_time_improvement:.1f}%")
                logger.info(f"平均速度提升: {avg_speed_improvement:.1f}%")
                
            logger.info("="*80)
            logger.info("实验完成！请查看生成的结果文件和图表。")
            
        except Exception as e:
            logger.error(f"实验过程中发生错误: {e}")
        finally:
            if self.neo4j_conn:
                self.neo4j_conn.close()
                
    def run_single_experiment(self, experiment_type: str):
        """运行单个实验"""
        if not self.setup_connection():
            logger.error("无法连接到Neo4j，实验终止")
            return
            
        try:
            if experiment_type.lower() == 'async':
                return asyncio.run(self.run_async_experiment())
            elif experiment_type.lower() == 'sync':
                return self.run_sync_experiment()
            elif experiment_type.lower() == 'compare':
                return self.generate_comparison_report()
            else:
                logger.error(f"未知的实验类型: {experiment_type}")
                return None
        finally:
            if self.neo4j_conn:
                self.neo4j_conn.close()

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='批次导入性能对比实验')
    parser.add_argument('--type', choices=['all', 'async', 'sync', 'compare'], 
                       default='all', help='实验类型 (默认: all)')
    parser.add_argument('--sizes', nargs='+', type=int, 
                       help='自定义批次大小 (例如: --sizes 10 50 100)')
    
    args = parser.parse_args()
    
    runner = BatchExperimentRunner()
    
    # 如果指定了自定义批次大小
    if args.sizes:
        runner.batch_sizes = args.sizes
        logger.info(f"使用自定义批次大小: {runner.batch_sizes}")
    
    if args.type == 'all':
        asyncio.run(runner.run_all_experiments())
    else:
        runner.run_single_experiment(args.type)

if __name__ == "__main__":
    main()
