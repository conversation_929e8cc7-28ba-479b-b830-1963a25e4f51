#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步vs同步导入性能对比分析工具
"""

import json
import logging
import os
import sys
from datetime import datetime
from typing import Dict, List, Any
import matplotlib.pyplot as plt
import pandas as pd

# 配置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PerformanceComparator:
    def __init__(self):
        self.async_results = None
        self.sync_results = None
        
    def load_results(self, async_file: str = None, sync_file: str = None):
        """加载异步和同步实验结果"""
        # 如果没有指定文件，自动查找最新的结果文件
        if not async_file:
            async_file = self.find_latest_result_file("async_batch_import_results_")
        if not sync_file:
            sync_file = self.find_latest_result_file("sync_batch_import_results_")
            
        try:
            if async_file and os.path.exists(async_file):
                with open(async_file, 'r', encoding='utf-8') as f:
                    self.async_results = json.load(f)
                logger.info(f"加载异步结果: {async_file}")
            else:
                logger.warning("未找到异步实验结果文件")
                
            if sync_file and os.path.exists(sync_file):
                with open(sync_file, 'r', encoding='utf-8') as f:
                    self.sync_results = json.load(f)
                logger.info(f"加载同步结果: {sync_file}")
            else:
                logger.warning("未找到同步实验结果文件")
                
        except Exception as e:
            logger.error(f"加载结果文件失败: {e}")
            
    def find_latest_result_file(self, prefix: str) -> str:
        """查找最新的结果文件"""
        try:
            files = [f for f in os.listdir('.') if f.startswith(prefix) and f.endswith('.json')]
            if files:
                # 按修改时间排序，返回最新的
                latest_file = max(files, key=lambda x: os.path.getmtime(x))
                return latest_file
        except Exception as e:
            logger.error(f"查找结果文件失败: {e}")
        return None
        
    def generate_comparison_report(self):
        """生成对比报告"""
        if not self.async_results or not self.sync_results:
            logger.error("缺少实验结果数据，无法生成对比报告")
            return
            
        logger.info("\n" + "="*80)
        logger.info("异步 vs 同步导入性能对比报告")
        logger.info("="*80)
        
        # 创建对比表格
        comparison_data = []
        
        # 按批次大小匹配结果
        async_dict = {r['total_nodes']: r for r in self.async_results}
        sync_dict = {r['total_nodes']: r for r in self.sync_results}
        
        common_sizes = set(async_dict.keys()) & set(sync_dict.keys())
        
        for size in sorted(common_sizes):
            async_r = async_dict[size]
            sync_r = sync_dict[size]
            
            # 计算性能提升
            time_improvement = ((sync_r['duration_seconds'] - async_r['duration_seconds']) / sync_r['duration_seconds']) * 100
            speed_improvement = ((async_r['nodes_per_second'] - sync_r['nodes_per_second']) / sync_r['nodes_per_second']) * 100
            
            comparison_data.append({
                'batch_size': size,
                'async_time': async_r['duration_seconds'],
                'sync_time': sync_r['duration_seconds'],
                'async_speed': async_r['nodes_per_second'],
                'sync_speed': sync_r['nodes_per_second'],
                'time_improvement_pct': time_improvement,
                'speed_improvement_pct': speed_improvement,
                'async_success_rate': async_r['success_count'] / async_r['total_nodes'] * 100,
                'sync_success_rate': sync_r['success_count'] / sync_r['total_nodes'] * 100
            })
        
        # 打印详细对比表格
        logger.info(f"{'批次大小':<8} {'异步耗时':<10} {'同步耗时':<10} {'时间提升':<10} {'异步速度':<10} {'同步速度':<10} {'速度提升':<10}")
        logger.info("-" * 80)
        
        for data in comparison_data:
            logger.info(f"{data['batch_size']:<8} "
                       f"{data['async_time']:<10.2f} "
                       f"{data['sync_time']:<10.2f} "
                       f"{data['time_improvement_pct']:<10.1f}% "
                       f"{data['async_speed']:<10.2f} "
                       f"{data['sync_speed']:<10.2f} "
                       f"{data['speed_improvement_pct']:<10.1f}%")
        
        # 计算总体统计
        avg_time_improvement = sum(d['time_improvement_pct'] for d in comparison_data) / len(comparison_data)
        avg_speed_improvement = sum(d['speed_improvement_pct'] for d in comparison_data) / len(comparison_data)
        
        logger.info("-" * 80)
        logger.info(f"平均时间提升: {avg_time_improvement:.1f}%")
        logger.info(f"平均速度提升: {avg_speed_improvement:.1f}%")
        logger.info("="*80)
        
        # 保存对比数据
        self.save_comparison_data(comparison_data)
        
        # 生成图表
        self.generate_charts(comparison_data)
        
        return comparison_data
        
    def save_comparison_data(self, comparison_data: List[Dict]):
        """保存对比数据"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"performance_comparison_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(comparison_data, f, ensure_ascii=False, indent=2)
            logger.info(f"对比数据已保存到: {filename}")
        except Exception as e:
            logger.error(f"保存对比数据失败: {e}")
            
    def generate_charts(self, comparison_data: List[Dict]):
        """生成性能对比图表"""
        try:
            # 创建图表
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
            
            batch_sizes = [d['batch_size'] for d in comparison_data]
            async_times = [d['async_time'] for d in comparison_data]
            sync_times = [d['sync_time'] for d in comparison_data]
            async_speeds = [d['async_speed'] for d in comparison_data]
            sync_speeds = [d['sync_speed'] for d in comparison_data]
            
            # 1. 执行时间对比
            ax1.plot(batch_sizes, async_times, 'b-o', label='异步导入', linewidth=2, markersize=6)
            ax1.plot(batch_sizes, sync_times, 'r-s', label='同步导入', linewidth=2, markersize=6)
            ax1.set_xlabel('批次大小（节点数）')
            ax1.set_ylabel('执行时间（秒）')
            ax1.set_title('执行时间对比')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # 2. 处理速度对比
            ax2.plot(batch_sizes, async_speeds, 'b-o', label='异步导入', linewidth=2, markersize=6)
            ax2.plot(batch_sizes, sync_speeds, 'r-s', label='同步导入', linewidth=2, markersize=6)
            ax2.set_xlabel('批次大小（节点数）')
            ax2.set_ylabel('处理速度（节点/秒）')
            ax2.set_title('处理速度对比')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            
            # 3. 性能提升百分比
            time_improvements = [d['time_improvement_pct'] for d in comparison_data]
            speed_improvements = [d['speed_improvement_pct'] for d in comparison_data]
            
            ax3.bar([x - 0.2 for x in batch_sizes], time_improvements, width=0.4, label='时间提升%', alpha=0.7)
            ax3.bar([x + 0.2 for x in batch_sizes], speed_improvements, width=0.4, label='速度提升%', alpha=0.7)
            ax3.set_xlabel('批次大小（节点数）')
            ax3.set_ylabel('提升百分比（%）')
            ax3.set_title('异步相对于同步的性能提升')
            ax3.legend()
            ax3.grid(True, alpha=0.3)
            ax3.axhline(y=0, color='black', linestyle='-', alpha=0.3)
            
            # 4. 成功率对比
            async_success_rates = [d['async_success_rate'] for d in comparison_data]
            sync_success_rates = [d['sync_success_rate'] for d in comparison_data]
            
            ax4.plot(batch_sizes, async_success_rates, 'b-o', label='异步导入', linewidth=2, markersize=6)
            ax4.plot(batch_sizes, sync_success_rates, 'r-s', label='同步导入', linewidth=2, markersize=6)
            ax4.set_xlabel('批次大小（节点数）')
            ax4.set_ylabel('成功率（%）')
            ax4.set_title('导入成功率对比')
            ax4.legend()
            ax4.grid(True, alpha=0.3)
            ax4.set_ylim(0, 105)
            
            plt.tight_layout()
            
            # 保存图表
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            chart_filename = f"performance_comparison_charts_{timestamp}.png"
            plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
            logger.info(f"性能对比图表已保存到: {chart_filename}")
            
            # 显示图表
            plt.show()
            
        except Exception as e:
            logger.error(f"生成图表失败: {e}")

def main():
    """主函数"""
    comparator = PerformanceComparator()
    
    # 加载实验结果
    comparator.load_results()
    
    # 生成对比报告
    comparison_data = comparator.generate_comparison_report()
    
    if comparison_data:
        logger.info("性能对比分析完成！")
    else:
        logger.error("性能对比分析失败，请确保已运行异步和同步导入实验")

if __name__ == "__main__":
    main()
