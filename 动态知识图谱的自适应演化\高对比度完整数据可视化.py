#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高对比度版本 - 完整数据可视化对比 (保持原图布局，仅加深颜色)
"""

import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime
import json

# 配置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300

def create_high_contrast_complete_visualization():
    """创建高对比度版本的完整可视化 - 保持原图布局"""
    
    # 实际测量数据 (与原图完全相同)
    batch_sizes = [10, 50, 100, 150, 200, 250]
    
    # 简化异步处理数据 (实际测量)
    simplified_async_times = [70.50, 238.30, 761.39, 1177.43, 1366.78, 2187.05]
    simplified_async_speeds = [0.142, 0.210, 0.131, 0.127, 0.146, 0.114]
    
    # 同步处理数据 (实际测量)
    sync_times = [118, 556, 1207, 1569, 1909, 2167]
    sync_speeds = [0.085, 0.090, 0.083, 0.096, 0.105, 0.115]
    
    # 最新异步处理数据 (250个节点实际测量，其他预测)
    improvement_factor = 0.334  # 时间改进33.4%
    speed_improvement = 0.509   # 速度提升50.9%
    
    # 预测最新异步的其他批次数据 (与原图算法相同)
    latest_async_times = []
    latest_async_speeds = []
    
    for i, size in enumerate(batch_sizes):
        if size == 250:
            # 实际测量数据
            latest_async_times.append(1456.84)
            latest_async_speeds.append(0.172)
        else:
            # 基于改进幅度预测
            if size <= 50:
                predicted_improvement = 0.20 + (size / 50) * 0.05  # 20%-25%
            elif size <= 150:
                predicted_improvement = 0.25 + ((size - 50) / 100) * 0.05  # 25%-30%
            else:
                predicted_improvement = 0.30 + ((size - 150) / 100) * 0.03  # 30%-33%
            
            predicted_time = simplified_async_times[i] * (1 - predicted_improvement)
            predicted_speed = simplified_async_speeds[i] * (1 + predicted_improvement * 1.5)
            
            latest_async_times.append(predicted_time)
            latest_async_speeds.append(predicted_speed)
    
    # 高对比度颜色方案 (加深原图颜色)
    colors = {
        'simplified_async': '#2E7D32',    # 深绿色 (原:#4CAF50)
        'sync': '#1565C0',               # 深蓝色 (原:#2196F3)  
        'latest_async': '#E65100',       # 深橙色 (原:#FF9800)
    }
    
    # 创建2x3子图 (与原图完全相同的布局)
    fig, ((ax1, ax2, ax3), (ax4, ax5, ax6)) = plt.subplots(2, 3, figsize=(20, 12))
    
    # 1. 处理时间对比 (与原图完全相同的布局)
    x = np.arange(len(batch_sizes))
    width = 0.25
    
    bars1 = ax1.bar(x - width, simplified_async_times, width, label='简化异步处理', 
                    color=colors['simplified_async'], alpha=0.8)
    bars2 = ax1.bar(x, sync_times, width, label='同步处理', 
                    color=colors['sync'], alpha=0.8)
    bars3 = ax1.bar(x + width, latest_async_times, width, label='最新异步处理', 
                    color=colors['latest_async'], alpha=0.8)
    
    ax1.set_xlabel('批次大小 (个景点)')
    ax1.set_ylabel('处理时间 (秒)')
    ax1.set_title('处理时间完整对比 (含预测数据)')
    ax1.set_xticks(x)
    ax1.set_xticklabels(batch_sizes)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 添加数值标签 (与原图相同)
    for i, (bar1, bar2, bar3) in enumerate(zip(bars1, bars2, bars3)):
        # 简化异步
        height1 = bar1.get_height()
        ax1.text(bar1.get_x() + bar1.get_width()/2., height1 + height1*0.01,
                f'{int(height1)}s', ha='center', va='bottom', fontsize=8, 
                color=colors['simplified_async'], fontweight='bold')
        
        # 同步
        height2 = bar2.get_height()
        ax1.text(bar2.get_x() + bar2.get_width()/2., height2 + height2*0.01,
                f'{int(height2)}s', ha='center', va='bottom', fontsize=8, 
                color=colors['sync'], fontweight='bold')
        
        # 最新异步
        height3 = bar3.get_height()
        label_suffix = "" if batch_sizes[i] == 250 else "*"
        ax1.text(bar3.get_x() + bar3.get_width()/2., height3 + height3*0.01,
                f'{int(height3)}s{label_suffix}', ha='center', va='bottom', fontsize=8, 
                color=colors['latest_async'], fontweight='bold')
    
    # 2. 处理速度对比 (与原图相同)
    ax2.plot(batch_sizes, simplified_async_speeds, 'o-', label='简化异步处理', 
             linewidth=3, markersize=8, color=colors['simplified_async'])
    ax2.plot(batch_sizes, sync_speeds, 's-', label='同步处理', 
             linewidth=3, markersize=8, color=colors['sync'])
    ax2.plot(batch_sizes, latest_async_speeds, 'D-', label='最新异步处理', 
             linewidth=3, markersize=8, color=colors['latest_async'])
    
    ax2.set_xlabel('批次大小 (个景点)')
    ax2.set_ylabel('处理速度 (节点/秒)')
    ax2.set_title('处理速度完整对比 (含预测数据)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(0, 0.30)
    
    # 添加数值标签 (与原图相同)
    for i, (size, simplified_speed, sync_speed, latest_speed) in enumerate(zip(batch_sizes, simplified_async_speeds, sync_speeds, latest_async_speeds)):
        ax2.annotate(f'{simplified_speed:.3f}', (size, simplified_speed), textcoords="offset points", 
                    xytext=(0,10), ha='center', fontsize=8, color=colors['simplified_async'], fontweight='bold')
        ax2.annotate(f'{sync_speed:.3f}', (size, sync_speed), textcoords="offset points", 
                    xytext=(0,-15), ha='center', fontsize=8, color=colors['sync'], fontweight='bold')
        
        label_suffix = "" if size == 250 else "*"
        ax2.annotate(f'{latest_speed:.3f}{label_suffix}', (size, latest_speed), textcoords="offset points", 
                    xytext=(0,15), ha='center', fontsize=8, color=colors['latest_async'], fontweight='bold')
    
    # 3. 性能优势对比 (与原图相同)
    x_pos = np.arange(len(batch_sizes))
    width = 0.35
    
    # 计算优势
    simplified_advantages = [(sync_times[i] - simplified_async_times[i]) / sync_times[i] * 100 for i in range(len(batch_sizes))]
    latest_advantages = [(sync_times[i] - latest_async_times[i]) / sync_times[i] * 100 for i in range(len(batch_sizes))]
    
    bars1 = ax3.bar(x_pos - width/2, simplified_advantages, width, 
                    color=colors['simplified_async'], alpha=0.8, label='简化异步优势')
    bars2 = ax3.bar(x_pos + width/2, latest_advantages, width, 
                    color=colors['latest_async'], alpha=0.8, label='最新异步优势')
    
    ax3.set_xlabel('批次大小 (个景点)')
    ax3.set_ylabel('时间节省百分比 (%)')
    ax3.set_title('异步处理相对于同步的优势')
    ax3.set_xticks(x_pos)
    ax3.set_xticklabels(batch_sizes)
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    ax3.axhline(y=0, color='red', linestyle='--', alpha=0.5)
    
    # 添加数值标签 (与原图相同)
    for i, (bar1, bar2, adv1, adv2) in enumerate(zip(bars1, bars2, simplified_advantages, latest_advantages)):
        height1 = bar1.get_height()
        color1 = colors['simplified_async'] if height1 > 0 else '#FF5722'
        ax3.text(bar1.get_x() + bar1.get_width()/2., height1 + (1 if height1 > 0 else -3),
                f'{adv1:.1f}%', ha='center', va='bottom' if height1 > 0 else 'top', 
                fontsize=8, fontweight='bold', color=color1)
        
        height2 = bar2.get_height()
        color2 = colors['latest_async'] if height2 > 0 else '#FF5722'
        label_suffix = "" if batch_sizes[i] == 250 else "*"
        ax3.text(bar2.get_x() + bar2.get_width()/2., height2 + (1 if height2 > 0 else -3),
                f'{adv2:.1f}%{label_suffix}', ha='center', va='bottom' if height2 > 0 else 'top', 
                fontsize=8, fontweight='bold', color=color2)
    
    # 4. 每节点处理时间对比 (与原图相同)
    simplified_per_node = [t/s for t, s in zip(simplified_async_times, batch_sizes)]
    sync_per_node = [t/s for t, s in zip(sync_times, batch_sizes)]
    latest_per_node = [t/s for t, s in zip(latest_async_times, batch_sizes)]
    
    ax4.plot(batch_sizes, simplified_per_node, 'o-', label='简化异步处理', 
             linewidth=3, markersize=8, color=colors['simplified_async'])
    ax4.plot(batch_sizes, sync_per_node, 's-', label='同步处理', 
             linewidth=3, markersize=8, color=colors['sync'])
    ax4.plot(batch_sizes, latest_per_node, 'D-', label='最新异步处理', 
             linewidth=3, markersize=8, color=colors['latest_async'])
    
    ax4.set_xlabel('批次大小 (个景点)')
    ax4.set_ylabel('每节点处理时间 (秒)')
    ax4.set_title('单节点处理时间对比')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    # 5. 累积时间节省 (与原图相同)
    cumulative_simplified_savings = []
    cumulative_latest_savings = []
    cumulative_sync_time = []
    
    for i in range(len(batch_sizes)):
        if i == 0:
            cumulative_simplified_savings.append(sync_times[i] - simplified_async_times[i])
            cumulative_latest_savings.append(sync_times[i] - latest_async_times[i])
            cumulative_sync_time.append(sync_times[i])
        else:
            cumulative_simplified_savings.append(cumulative_simplified_savings[i-1] + (sync_times[i] - simplified_async_times[i]))
            cumulative_latest_savings.append(cumulative_latest_savings[i-1] + (sync_times[i] - latest_async_times[i]))
            cumulative_sync_time.append(cumulative_sync_time[i-1] + sync_times[i])
    
    ax5.plot(batch_sizes, [s/60 for s in cumulative_simplified_savings], 'o-', label='简化异步累积节省', 
             linewidth=3, markersize=8, color=colors['simplified_async'])
    ax5.plot(batch_sizes, [s/60 for s in cumulative_latest_savings], 'D-', label='最新异步累积节省', 
             linewidth=3, markersize=8, color=colors['latest_async'])
    
    ax5.set_xlabel('批次大小 (个景点)')
    ax5.set_ylabel('累积时间节省 (分钟)')
    ax5.set_title('累积时间节省对比')
    ax5.legend()
    ax5.grid(True, alpha=0.3)
    
    # 6. 综合性能雷达图 (与原图相同)
    categories = ['小规模\n(≤50节点)', '中等规模\n(100-150节点)', '大规模\n(≥200节点)', '稳定性', '易维护性']
    
    # 评分 (1-5分)
    sync_scores = [2, 3, 4, 5, 4]
    simplified_async_scores = [5, 4, 3, 5, 4]
    latest_async_scores = [5, 5, 5, 5, 4]
    
    # 雷达图角度
    angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
    angles += angles[:1]
    
    # 数据闭合
    sync_scores += sync_scores[:1]
    simplified_async_scores += simplified_async_scores[:1]
    latest_async_scores += latest_async_scores[:1]
    
    ax6 = plt.subplot(2, 3, 6, projection='polar')
    ax6.plot(angles, sync_scores, 'o-', linewidth=2, label='同步处理', color=colors['sync'])
    ax6.fill(angles, sync_scores, alpha=0.25, color=colors['sync'])
    ax6.plot(angles, simplified_async_scores, 's-', linewidth=2, label='简化异步', color=colors['simplified_async'])
    ax6.fill(angles, simplified_async_scores, alpha=0.25, color=colors['simplified_async'])
    ax6.plot(angles, latest_async_scores, 'D-', linewidth=2, label='最新异步', color=colors['latest_async'])
    ax6.fill(angles, latest_async_scores, alpha=0.25, color=colors['latest_async'])
    
    ax6.set_xticks(angles[:-1])
    ax6.set_xticklabels(categories)
    ax6.set_ylim(0, 5)
    ax6.set_title('综合性能雷达图', pad=20)
    ax6.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    ax6.grid(True)
    
    # 添加说明 (与原图相同)
    fig.text(0.02, 0.02, '注: 标有*的数据为基于250个节点实测改进幅度的预测值', fontsize=10, style='italic')
    
    plt.tight_layout()
    
    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"高对比度完整数据可视化对比_{timestamp}.png"
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
    print(f"高对比度完整数据可视化对比图表已保存: {filename}")
    plt.close()
    
    return filename

if __name__ == "__main__":
    create_high_contrast_complete_visualization()
