#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高对比度版本 - 完整数据可视化对比 (保持原图布局，仅加深颜色)
"""

import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime
import json

# 配置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300

def create_high_contrast_complete_visualization():
    """创建高对比度版本的完整可视化 - 保持原图布局"""
    
    # 实际测量数据 (与原图完全相同)
    batch_sizes = [10, 50, 100, 150, 200, 250]
    
    # 简化异步处理数据 (实际测量)
    simplified_async_times = [70.50, 238.30, 761.39, 1177.43, 1366.78, 2187.05]
    simplified_async_speeds = [0.142, 0.210, 0.131, 0.127, 0.146, 0.114]
    
    # 同步处理数据 (实际测量)
    sync_times = [118, 556, 1207, 1569, 1909, 2167]
    sync_speeds = [0.085, 0.090, 0.083, 0.096, 0.105, 0.115]
    
    # 最新异步处理数据 (仅250个节点实际测量数据)
    latest_async_times = [None, None, None, None, None, 1456.84]  # 只有250节点有实测数据
    latest_async_speeds = [None, None, None, None, None, 0.172]   # 只有250节点有实测数据
    
    # 高对比度颜色方案 (加深原图颜色)
    colors = {
        'simplified_async': '#2E7D32',    # 深绿色 (原:#4CAF50)
        'sync': '#1565C0',               # 深蓝色 (原:#2196F3)  
        'latest_async': '#E65100',       # 深橙色 (原:#FF9800)
    }
    
    # 创建2x3子图 (与原图完全相同的布局)
    fig, ((ax1, ax2, ax3), (ax4, ax5, ax6)) = plt.subplots(2, 3, figsize=(20, 12))
    
    # 1. 处理时间对比 (仅实测数据)
    x = np.arange(len(batch_sizes))
    width = 0.35

    bars1 = ax1.bar(x - width/2, simplified_async_times, width, label='简化异步处理',
                    color=colors['simplified_async'], alpha=0.8)
    bars2 = ax1.bar(x + width/2, sync_times, width, label='同步处理',
                    color=colors['sync'], alpha=0.8)

    # 只在250节点位置添加最新异步数据
    latest_bar = ax1.bar([5], [latest_async_times[5]], width/2, label='最新异步处理',
                        color=colors['latest_async'], alpha=0.8)

    ax1.set_xlabel('批次大小 (个景点)')
    ax1.set_ylabel('处理时间 (秒)')
    ax1.set_title('处理时间对比 (实测数据)')
    ax1.set_xticks(x)
    ax1.set_xticklabels(batch_sizes)
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 添加数值标签 (仅实测数据)
    for i, (bar1, bar2) in enumerate(zip(bars1, bars2)):
        # 简化异步
        height1 = bar1.get_height()
        ax1.text(bar1.get_x() + bar1.get_width()/2., height1 + height1*0.01,
                f'{int(height1)}s', ha='center', va='bottom', fontsize=8,
                color=colors['simplified_async'], fontweight='bold')

        # 同步
        height2 = bar2.get_height()
        ax1.text(bar2.get_x() + bar2.get_width()/2., height2 + height2*0.01,
                f'{int(height2)}s', ha='center', va='bottom', fontsize=8,
                color=colors['sync'], fontweight='bold')

    # 最新异步 (仅250节点)
    height3 = latest_bar[0].get_height()
    ax1.text(latest_bar[0].get_x() + latest_bar[0].get_width()/2., height3 + height3*0.01,
            f'{int(height3)}s', ha='center', va='bottom', fontsize=8,
            color=colors['latest_async'], fontweight='bold')
    
    # 2. 处理速度对比 (仅实测数据)
    ax2.plot(batch_sizes, simplified_async_speeds, 'o-', label='简化异步处理',
             linewidth=3, markersize=8, color=colors['simplified_async'])
    ax2.plot(batch_sizes, sync_speeds, 's-', label='同步处理',
             linewidth=3, markersize=8, color=colors['sync'])
    # 只显示250节点的最新异步数据
    ax2.plot([250], [latest_async_speeds[5]], 'D', label='最新异步处理',
             markersize=10, color=colors['latest_async'])

    ax2.set_xlabel('批次大小 (个景点)')
    ax2.set_ylabel('处理速度 (节点/秒)')
    ax2.set_title('处理速度对比 (实测数据)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(0, 0.25)

    # 添加数值标签 (仅实测数据)
    for i, (size, simplified_speed, sync_speed) in enumerate(zip(batch_sizes, simplified_async_speeds, sync_speeds)):
        ax2.annotate(f'{simplified_speed:.3f}', (size, simplified_speed), textcoords="offset points",
                    xytext=(0,10), ha='center', fontsize=8, color=colors['simplified_async'], fontweight='bold')
        ax2.annotate(f'{sync_speed:.3f}', (size, sync_speed), textcoords="offset points",
                    xytext=(0,-15), ha='center', fontsize=8, color=colors['sync'], fontweight='bold')

    # 最新异步 (仅250节点)
    ax2.annotate(f'{latest_async_speeds[5]:.3f}', (250, latest_async_speeds[5]), textcoords="offset points",
                xytext=(0,15), ha='center', fontsize=8, color=colors['latest_async'], fontweight='bold')
    
    # 3. 性能优势对比 (仅实测数据)
    x_pos = np.arange(len(batch_sizes))
    width = 0.35

    # 计算优势 (仅简化异步)
    simplified_advantages = [(sync_times[i] - simplified_async_times[i]) / sync_times[i] * 100 for i in range(len(batch_sizes))]
    # 最新异步仅250节点
    latest_advantage_250 = (sync_times[5] - latest_async_times[5]) / sync_times[5] * 100

    bars1 = ax3.bar(x_pos, simplified_advantages, width,
                    color=colors['simplified_async'], alpha=0.8, label='简化异步优势')
    # 只在250节点位置显示最新异步优势
    bar2 = ax3.bar([5], [latest_advantage_250], width/2,
                   color=colors['latest_async'], alpha=0.8, label='最新异步优势')

    ax3.set_xlabel('批次大小 (个景点)')
    ax3.set_ylabel('时间节省百分比 (%)')
    ax3.set_title('异步处理相对于同步的优势')
    ax3.set_xticks(x_pos)
    ax3.set_xticklabels(batch_sizes)
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    ax3.axhline(y=0, color='red', linestyle='--', alpha=0.5)

    # 添加数值标签 (仅实测数据)
    for i, (bar1, adv1) in enumerate(zip(bars1, simplified_advantages)):
        height1 = bar1.get_height()
        color1 = colors['simplified_async'] if height1 > 0 else '#FF5722'
        ax3.text(bar1.get_x() + bar1.get_width()/2., height1 + (1 if height1 > 0 else -3),
                f'{adv1:.1f}%', ha='center', va='bottom' if height1 > 0 else 'top',
                fontsize=8, fontweight='bold', color=color1)

    # 最新异步 (仅250节点)
    height2 = bar2[0].get_height()
    ax3.text(bar2[0].get_x() + bar2[0].get_width()/2., height2 + 1,
            f'{latest_advantage_250:.1f}%', ha='center', va='bottom',
            fontsize=8, fontweight='bold', color=colors['latest_async'])
    
    # 4. 每节点处理时间对比 (仅实测数据)
    simplified_per_node = [t/s for t, s in zip(simplified_async_times, batch_sizes)]
    sync_per_node = [t/s for t, s in zip(sync_times, batch_sizes)]
    latest_per_node_250 = latest_async_times[5] / batch_sizes[5]

    ax4.plot(batch_sizes, simplified_per_node, 'o-', label='简化异步处理',
             linewidth=3, markersize=8, color=colors['simplified_async'])
    ax4.plot(batch_sizes, sync_per_node, 's-', label='同步处理',
             linewidth=3, markersize=8, color=colors['sync'])
    # 只显示250节点的最新异步数据
    ax4.plot([250], [latest_per_node_250], 'D', label='最新异步处理',
             markersize=10, color=colors['latest_async'])

    ax4.set_xlabel('批次大小 (个景点)')
    ax4.set_ylabel('每节点处理时间 (秒)')
    ax4.set_title('单节点处理时间对比')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    # 5. 累积时间节省 (仅实测数据)
    cumulative_simplified_savings = []

    for i in range(len(batch_sizes)):
        if i == 0:
            cumulative_simplified_savings.append(sync_times[i] - simplified_async_times[i])
        else:
            cumulative_simplified_savings.append(cumulative_simplified_savings[i-1] + (sync_times[i] - simplified_async_times[i]))

    ax5.plot(batch_sizes, [s/60 for s in cumulative_simplified_savings], 'o-', label='简化异步累积节省',
             linewidth=3, markersize=8, color=colors['simplified_async'])

    # 只显示250节点的最新异步累积节省
    latest_cumulative_250 = cumulative_simplified_savings[4] + (sync_times[5] - latest_async_times[5])
    ax5.plot([250], [latest_cumulative_250/60], 'D', label='最新异步累积节省',
             markersize=10, color=colors['latest_async'])

    ax5.set_xlabel('批次大小 (个景点)')
    ax5.set_ylabel('累积时间节省 (分钟)')
    ax5.set_title('累积时间节省对比')
    ax5.legend()
    ax5.grid(True, alpha=0.3)
    
    # 6. 综合性能雷达图 (与原图相同)
    categories = ['小规模\n(≤50节点)', '中等规模\n(100-150节点)', '大规模\n(≥200节点)', '稳定性', '易维护性']
    
    # 评分 (1-5分)
    sync_scores = [2, 3, 4, 5, 4]
    simplified_async_scores = [5, 4, 3, 5, 4]
    latest_async_scores = [5, 5, 5, 5, 4]
    
    # 雷达图角度
    angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
    angles += angles[:1]
    
    # 数据闭合
    sync_scores += sync_scores[:1]
    simplified_async_scores += simplified_async_scores[:1]
    latest_async_scores += latest_async_scores[:1]
    
    ax6 = plt.subplot(2, 3, 6, projection='polar')
    ax6.plot(angles, sync_scores, 'o-', linewidth=2, label='同步处理', color=colors['sync'])
    ax6.fill(angles, sync_scores, alpha=0.25, color=colors['sync'])
    ax6.plot(angles, simplified_async_scores, 's-', linewidth=2, label='简化异步', color=colors['simplified_async'])
    ax6.fill(angles, simplified_async_scores, alpha=0.25, color=colors['simplified_async'])
    ax6.plot(angles, latest_async_scores, 'D-', linewidth=2, label='最新异步', color=colors['latest_async'])
    ax6.fill(angles, latest_async_scores, alpha=0.25, color=colors['latest_async'])
    
    ax6.set_xticks(angles[:-1])
    ax6.set_xticklabels(categories)
    ax6.set_ylim(0, 5)
    ax6.set_title('综合性能雷达图', pad=20)
    ax6.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    ax6.grid(True)
    
    # 添加说明 (仅实测数据)
    fig.text(0.02, 0.02, '注: 所有数据均为实际测量结果，最新异步处理仅有250个节点的实测数据', fontsize=10, style='italic')

    plt.tight_layout()

    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"高对比度实测数据可视化对比_{timestamp}.png"
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
    print(f"高对比度实测数据可视化对比图表已保存: {filename}")
    plt.close()

    return filename

if __name__ == "__main__":
    create_high_contrast_complete_visualization()
