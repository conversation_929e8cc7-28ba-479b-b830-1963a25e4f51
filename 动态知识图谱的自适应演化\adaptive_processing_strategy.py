#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自适应处理策略 - 根据批次大小智能选择最优处理方式
"""

import asyncio
import json
import logging
import os
import sys
import time
from datetime import datetime
from typing import Dict, List, Any, Literal
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from neo4j_connection import Neo4jConnection
from knowledge_graph_updater import KnowledgeGraphUpdater
from simplified_async_import import SimplifiedAsyncImporter
from batch_sync_import import BatchSyncImporter

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('adaptive_processing.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


class AdaptiveProcessingStrategy:
    """自适应处理策略 - 根据数据规模智能选择最优方案"""
    
    def __init__(self, neo4j_conn: Neo4jConnection):
        self.neo4j_conn = neo4j_conn
        self.async_importer = SimplifiedAsyncImporter(neo4j_conn)
        self.sync_importer = BatchSyncImporter(neo4j_conn)
        
        # 性能阈值配置
        self.performance_thresholds = {
            "small_batch_max": 100,      # 小批次上限
            "medium_batch_max": 200,     # 中批次上限
            "async_advantage_threshold": 0.15,  # 异步优势阈值(15%)
        }
        
        # 历史性能数据（基于实验结果）
        self.performance_data = {
            "async": {
                10: {"time": 70.50, "speed": 0.142},
                50: {"time": 238.30, "speed": 0.210},
                100: {"time": 761.39, "speed": 0.131},
                150: {"time": 1177.43, "speed": 0.127},
                200: {"time": 1366.78, "speed": 0.146},
                250: {"time": 2187.05, "speed": 0.114}
            },
            "sync": {
                10: {"time": 118, "speed": 0.085},
                50: {"time": 556, "speed": 0.090},
                100: {"time": 1207, "speed": 0.083},
                150: {"time": 1569, "speed": 0.096},
                200: {"time": 1909, "speed": 0.105},
                250: {"time": 2167, "speed": 0.115}
            }
        }
    
    def choose_optimal_strategy(self, batch_size: int) -> Literal["async", "sync"]:
        """根据批次大小选择最优处理策略"""
        
        # 预测两种方案的性能
        async_prediction = self._predict_performance("async", batch_size)
        sync_prediction = self._predict_performance("sync", batch_size)
        
        # 计算异步相对于同步的优势
        time_advantage = (sync_prediction["time"] - async_prediction["time"]) / sync_prediction["time"]
        
        logger.info(f"批次大小 {batch_size} 的性能预测:")
        logger.info(f"  异步预测: {async_prediction['time']:.1f}秒, {async_prediction['speed']:.3f}节点/秒")
        logger.info(f"  同步预测: {sync_prediction['time']:.1f}秒, {sync_prediction['speed']:.3f}节点/秒")
        logger.info(f"  异步时间优势: {time_advantage*100:.1f}%")
        
        # 决策逻辑
        if batch_size <= self.performance_thresholds["small_batch_max"]:
            # 小批次：异步通常有明显优势
            if time_advantage > self.performance_thresholds["async_advantage_threshold"]:
                logger.info(f"选择策略: 异步处理 (小批次，优势明显)")
                return "async"
            else:
                logger.info(f"选择策略: 同步处理 (小批次，但异步优势不明显)")
                return "sync"
        
        elif batch_size <= self.performance_thresholds["medium_batch_max"]:
            # 中批次：根据预测优势选择
            if time_advantage > 0.05:  # 5%以上优势
                logger.info(f"选择策略: 异步处理 (中批次，有优势)")
                return "async"
            else:
                logger.info(f"选择策略: 同步处理 (中批次，优势不明显)")
                return "sync"
        
        else:
            # 大批次：倾向于同步处理
            if time_advantage > 0.20:  # 需要20%以上的明显优势才选异步
                logger.info(f"选择策略: 异步处理 (大批次，但异步有显著优势)")
                return "async"
            else:
                logger.info(f"选择策略: 同步处理 (大批次，同步扩展性更好)")
                return "sync"
    
    def _predict_performance(self, strategy: str, batch_size: int) -> Dict[str, float]:
        """基于历史数据预测性能"""
        data = self.performance_data[strategy]
        
        # 找到最接近的两个数据点进行插值
        sizes = sorted(data.keys())
        
        if batch_size <= sizes[0]:
            # 小于最小值，使用最小值的数据
            base_data = data[sizes[0]]
            return {
                "time": base_data["time"] * batch_size / sizes[0],
                "speed": base_data["speed"]
            }
        
        elif batch_size >= sizes[-1]:
            # 大于最大值，基于趋势外推
            base_data = data[sizes[-1]]
            if strategy == "async":
                # 异步处理在大批次时性能下降
                speed_factor = 0.95 ** ((batch_size - sizes[-1]) / 50)  # 每50个节点下降5%
            else:
                # 同步处理在大批次时性能提升
                speed_factor = 1.02 ** ((batch_size - sizes[-1]) / 50)  # 每50个节点提升2%
            
            predicted_speed = base_data["speed"] * speed_factor
            return {
                "time": batch_size / predicted_speed,
                "speed": predicted_speed
            }
        
        else:
            # 在范围内，线性插值
            for i in range(len(sizes) - 1):
                if sizes[i] <= batch_size <= sizes[i + 1]:
                    lower_size, upper_size = sizes[i], sizes[i + 1]
                    lower_data, upper_data = data[lower_size], data[upper_size]
                    
                    # 线性插值
                    ratio = (batch_size - lower_size) / (upper_size - lower_size)
                    predicted_time = lower_data["time"] + ratio * (upper_data["time"] - lower_data["time"])
                    predicted_speed = lower_data["speed"] + ratio * (upper_data["speed"] - lower_data["speed"])
                    
                    return {
                        "time": predicted_time,
                        "speed": predicted_speed
                    }
    
    async def process_batch_adaptive(self, nodes: List[Dict], batch_name: str) -> Dict:
        """自适应批处理 - 自动选择最优策略"""
        batch_size = len(nodes)
        logger.info(f"开始自适应处理: {batch_name}, 节点数: {batch_size}")
        
        # 选择最优策略
        strategy = self.choose_optimal_strategy(batch_size)
        
        start_time = time.time()
        
        try:
            if strategy == "async":
                # 使用异步处理
                result = await self.async_importer.import_batch_simplified(nodes, batch_name)
                result["strategy_used"] = "async"
                result["strategy_reason"] = "异步处理在此批次大小下有优势"
                
            else:
                # 使用同步处理
                result = await asyncio.to_thread(
                    self.sync_importer.import_batch_sync, nodes, batch_name
                )
                result["strategy_used"] = "sync"
                result["strategy_reason"] = "同步处理在此批次大小下更优"
            
            # 更新性能数据
            actual_time = time.time() - start_time
            actual_speed = batch_size / actual_time if actual_time > 0 else 0
            
            result.update({
                "actual_time": actual_time,
                "actual_speed": actual_speed,
                "batch_size": batch_size
            })
            
            logger.info(f"自适应处理完成: {batch_name}")
            logger.info(f"  使用策略: {strategy}")
            logger.info(f"  实际耗时: {actual_time:.2f}秒")
            logger.info(f"  实际速度: {actual_speed:.3f}节点/秒")
            
            return result
            
        except Exception as e:
            logger.error(f"自适应处理失败: {e}", exc_info=True)
            raise
    
    async def run_adaptive_experiment(self, json_file_path: str, batch_sizes: List[int]):
        """运行自适应处理实验"""
        logger.info(f"开始自适应处理实验，批次大小: {batch_sizes}")
        
        # 读取JSON数据
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            nodes = data.get("nodes", [])
            logger.info(f"读取到 {len(nodes)} 个节点")
        except Exception as e:
            logger.error(f"读取JSON文件失败: {e}")
            return
        
        # 去重
        seen_names = set()
        unique_nodes = []
        for node in nodes:
            if node["name"] not in seen_names:
                seen_names.add(node["name"])
                unique_nodes.append(node)
        logger.info(f"去重后节点数: {len(unique_nodes)}")
        
        # 按批次大小进行实验
        experiment_results = []
        
        for batch_size in batch_sizes:
            logger.info(f"\n{'='*60}")
            logger.info(f"开始自适应测试批次大小: {batch_size}")
            logger.info(f"{'='*60}")
            
            # 清空数据库
            try:
                self.neo4j_conn.clear_database()
                logger.info("数据库已清空")
            except Exception as e:
                logger.error(f"清空数据库失败: {e}")
                continue
            
            # 选择前N个节点
            batch_nodes = unique_nodes[:batch_size]
            batch_name = f"adaptive_batch_{batch_size}_nodes"
            
            # 执行自适应处理
            try:
                result = await self.process_batch_adaptive(batch_nodes, batch_name)
                experiment_results.append(result)
                
                # 记录结果
                logger.info(f"批次 {batch_size} 结果:")
                logger.info(f"  - 策略: {result['strategy_used']}")
                logger.info(f"  - 成功: {result.get('success_count', 0)}/{result.get('total_nodes', batch_size)}")
                logger.info(f"  - 耗时: {result.get('actual_time', 0):.2f} 秒")
                logger.info(f"  - 速度: {result.get('actual_speed', 0):.3f} 节点/秒")
                
            except Exception as e:
                logger.error(f"批次 {batch_size} 执行失败: {e}")
                continue
            
            # 等待一段时间再进行下一批次
            await asyncio.sleep(2)
        
        # 保存实验结果
        self.save_experiment_results(experiment_results)
        
        # 打印汇总报告
        self.print_summary_report(experiment_results)
        
        return experiment_results
    
    def save_experiment_results(self, results: List[Dict]):
        """保存实验结果到JSON文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"adaptive_processing_results_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            logger.info(f"实验结果已保存到: {filename}")
        except Exception as e:
            logger.error(f"保存实验结果失败: {e}")
    
    def print_summary_report(self, results: List[Dict]):
        """打印汇总报告"""
        logger.info(f"\n{'='*80}")
        logger.info("自适应处理实验汇总报告")
        logger.info(f"{'='*80}")
        
        async_count = sum(1 for r in results if r.get('strategy_used') == 'async')
        sync_count = sum(1 for r in results if r.get('strategy_used') == 'sync')
        
        logger.info(f"策略选择统计: 异步 {async_count} 次, 同步 {sync_count} 次")
        logger.info("")
        
        for result in results:
            batch_size = result.get('batch_size', 0)
            strategy = result.get('strategy_used', 'unknown')
            duration = result.get('actual_time', 0)
            speed = result.get('actual_speed', 0)
            success_rate = result.get('success_count', 0) / result.get('total_nodes', 1) * 100
            
            strategy_icon = "🚀" if strategy == "async" else "🔄"
            logger.info(f"{batch_size:>3}个节点: {strategy_icon} {strategy:<5} | {duration:>7.1f}秒 | {speed:.3f}节点/秒 | 成功率: {success_rate:.1f}%")
        
        logger.info(f"{'='*80}")
    
    def close(self):
        """关闭连接"""
        if hasattr(self, 'async_importer'):
            self.async_importer.close()
        if hasattr(self, 'sync_importer'):
            self.sync_importer.close()


async def main():
    """主函数"""
    processor = None
    try:
        # 连接Neo4j
        neo4j_config = Config.get_neo4j_config()
        neo4j_conn = Neo4jConnection(
            uri=neo4j_config["uri"],
            user=neo4j_config["user"],
            password=neo4j_config["password"]
        )
        neo4j_conn.verify_connectivity()
        logger.info("成功连接到Neo4j数据库")
        
        # 创建自适应处理器
        processor = AdaptiveProcessingStrategy(neo4j_conn)
        
        # 设置测试批次大小
        batch_sizes = [10, 50, 100, 150, 200, 250]
        
        # JSON文件路径
        json_file_path = os.path.join(os.path.dirname(__file__), "data", "lhasa_knowledge_graph.json")
        
        # 运行自适应实验
        results = await processor.run_adaptive_experiment(json_file_path, batch_sizes)
        
        logger.info("自适应处理实验完成！")
        
    except Exception as e:
        logger.error(f"主程序错误: {e}", exc_info=True)
    finally:
        if processor:
            processor.close()


if __name__ == "__main__":
    asyncio.run(main())
