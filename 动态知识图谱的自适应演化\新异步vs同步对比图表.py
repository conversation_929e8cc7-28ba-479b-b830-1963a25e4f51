#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于改进异步实验数据生成与同步处理的对比图表
"""

import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime

# 配置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def create_improved_async_vs_sync_charts():
    """基于改进异步数据创建对比图表"""
    
    # 实际实验数据
    batch_sizes = [10, 50, 100, 150, 200, 250]
    
    # 改进异步处理时间 (秒) - 实际测量数据
    improved_async_times = [70.50, 238.30, 761.39, 1177.43, 1366.78, 2187.05]
    
    # 同步处理时间 (秒) - 实际测量数据
    sync_times = [118, 556, 1207, 1569, 1909, 2167]
    
    # 处理速度 (节点/秒)
    improved_async_speeds = [0.142, 0.210, 0.131, 0.127, 0.146, 0.114]
    sync_speeds = [0.085, 0.090, 0.083, 0.096, 0.105, 0.115]
    
    # 每节点处理时间
    improved_async_per_node = [t/s for t, s in zip(improved_async_times, batch_sizes)]
    sync_per_node = [t/s for t, s in zip(sync_times, batch_sizes)]
    
    # 创建2x2子图
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. 处理时间对比 (柱状图)
    x = np.arange(len(batch_sizes))
    width = 0.35
    
    bars1 = ax1.bar(x - width/2, improved_async_times, width, label='改进异步处理', color='#4CAF50', alpha=0.8)
    bars2 = ax1.bar(x + width/2, sync_times, width, label='同步处理', color='#2196F3', alpha=0.8)
    
    ax1.set_xlabel('批次大小 (个景点)')
    ax1.set_ylabel('处理时间 (秒)')
    ax1.set_title('改进异步 vs 同步处理时间对比')
    ax1.set_xticks(x)
    ax1.set_xticklabels(batch_sizes)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 在柱子上添加数值标签
    for bar in bars1:
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{int(height)}s', ha='center', va='bottom', fontsize=8, color='#4CAF50', fontweight='bold')
    
    for bar in bars2:
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{int(height)}s', ha='center', va='bottom', fontsize=8, color='#2196F3', fontweight='bold')
    
    # 2. 处理速度对比 (折线图)
    ax2.plot(batch_sizes, improved_async_speeds, 'o-', label='改进异步处理', linewidth=3, markersize=8, color='#4CAF50')
    ax2.plot(batch_sizes, sync_speeds, 's-', label='同步处理', linewidth=3, markersize=8, color='#2196F3')
    ax2.set_xlabel('批次大小 (个景点)')
    ax2.set_ylabel('处理速度 (节点/秒)')
    ax2.set_title('处理速度对比')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(0, 0.25)
    
    # 添加数值标签
    for i, (size, async_speed, sync_speed) in enumerate(zip(batch_sizes, improved_async_speeds, sync_speeds)):
        ax2.annotate(f'{async_speed:.3f}', (size, async_speed), textcoords="offset points", 
                    xytext=(0,10), ha='center', fontsize=8, color='#4CAF50', fontweight='bold')
        ax2.annotate(f'{sync_speed:.3f}', (size, sync_speed), textcoords="offset points", 
                    xytext=(0,-15), ha='center', fontsize=8, color='#2196F3', fontweight='bold')
    
    # 3. 每节点处理时间对比
    ax3.plot(batch_sizes, improved_async_per_node, 'o-', label='改进异步处理', linewidth=3, markersize=8, color='#4CAF50')
    ax3.plot(batch_sizes, sync_per_node, 's-', label='同步处理', linewidth=3, markersize=8, color='#2196F3')
    ax3.set_xlabel('批次大小 (个景点)')
    ax3.set_ylabel('每节点处理时间 (秒)')
    ax3.set_title('单节点处理时间对比')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 性能优势百分比 (柱状图)
    time_improvements = [(s-a)/s*100 for a, s in zip(improved_async_times, sync_times)]
    speed_improvements = [(a-s)/s*100 for a, s in zip(improved_async_speeds, sync_speeds)]
    
    x = np.arange(len(batch_sizes))
    width = 0.35
    
    # 根据正负值设置颜色
    time_colors = ['#4CAF50' if diff > 0 else '#FF5722' for diff in time_improvements]
    speed_colors = ['#4CAF50' if diff > 0 else '#FF5722' for diff in speed_improvements]
    
    bars1 = ax4.bar(x - width/2, time_improvements, width, label='时间节省%', color=time_colors, alpha=0.8)
    bars2 = ax4.bar(x + width/2, speed_improvements, width, label='速度提升%', color=speed_colors, alpha=0.8)
    
    ax4.set_xlabel('批次大小 (个景点)')
    ax4.set_ylabel('性能提升百分比 (%)')
    ax4.set_title('改进异步相对于同步的性能优势\n(正值表示异步更优，负值表示同步更优)')
    ax4.set_xticks(x)
    ax4.set_xticklabels(batch_sizes)
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    ax4.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    
    # 添加数值标签
    for bar, diff in zip(bars1, time_improvements):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + (2 if height > 0 else -4),
                f'{diff:.1f}%', ha='center', va='bottom' if height > 0 else 'top', 
                fontsize=8, fontweight='bold')
    
    for bar, diff in zip(bars2, speed_improvements):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + (2 if height > 0 else -4),
                f'{diff:.1f}%', ha='center', va='bottom' if height > 0 else 'top', 
                fontsize=8, fontweight='bold')
    
    plt.tight_layout()
    
    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"改进异步vs同步对比_{timestamp}.png"
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"改进异步vs同步对比图表已保存: {filename}")
    
    return filename

def print_detailed_comparison():
    """打印详细对比分析"""
    print("\n" + "="*90)
    print("改进异步 vs 同步处理详细对比分析")
    print("="*90)
    
    batch_sizes = [10, 50, 100, 150, 200, 250]
    improved_async_times = [70.50, 238.30, 761.39, 1177.43, 1366.78, 2187.05]
    sync_times = [118, 556, 1207, 1569, 1909, 2167]
    improved_async_speeds = [0.142, 0.210, 0.131, 0.127, 0.146, 0.114]
    sync_speeds = [0.085, 0.090, 0.083, 0.096, 0.105, 0.115]
    
    print(f"{'批次':<6} {'改进异步':<10} {'同步':<10} {'时间节省':<10} {'异步速度':<10} {'同步速度':<10} {'速度提升':<10} {'推荐':<8}")
    print("-" * 90)
    
    for i, size in enumerate(batch_sizes):
        time_save = (sync_times[i] - improved_async_times[i]) / sync_times[i] * 100
        speed_improve = (improved_async_speeds[i] - sync_speeds[i]) / sync_speeds[i] * 100
        recommendation = "改进异步" if time_save > 5 else "相当" if abs(time_save) <= 5 else "同步"
        
        print(f"{size:<6} "
              f"{improved_async_times[i]:<10.1f} "
              f"{sync_times[i]:<10.1f} "
              f"{time_save:<10.1f}% "
              f"{improved_async_speeds[i]:<10.3f} "
              f"{sync_speeds[i]:<10.3f} "
              f"{speed_improve:<10.1f}% "
              f"{recommendation:<8}")
    
    print("-" * 90)
    
    # 计算平均性能提升
    avg_time_save = sum((sync_times[i] - improved_async_times[i]) / sync_times[i] * 100 for i in range(len(batch_sizes))) / len(batch_sizes)
    avg_speed_improve = sum((improved_async_speeds[i] - sync_speeds[i]) / sync_speeds[i] * 100 for i in range(len(batch_sizes))) / len(batch_sizes)
    
    print("关键发现:")
    print(f"1. 平均时间节省: {avg_time_save:.1f}%")
    print(f"2. 平均速度提升: {avg_speed_improve:.1f}%")
    print("3. 改进异步在10-200个节点时显著优于同步")
    print("4. 250个节点时两种方案性能基本相当")
    print("5. 智能并发控制成功解决了API限流问题")
    print("="*90)

def print_performance_summary():
    """打印性能汇总"""
    print("\n" + "🎯 性能汇总分析")
    print("-" * 50)
    
    batch_sizes = [10, 50, 100, 150, 200, 250]
    improved_async_times = [70.50, 238.30, 761.39, 1177.43, 1366.78, 2187.05]
    sync_times = [118, 556, 1207, 1569, 1909, 2167]
    
    print("📊 各批次大小的性能对比:")
    for i, size in enumerate(batch_sizes):
        time_save = sync_times[i] - improved_async_times[i]
        time_save_pct = time_save / sync_times[i] * 100
        
        if time_save_pct > 20:
            status = "🏆 显著优势"
        elif time_save_pct > 5:
            status = "✅ 明显优势"
        elif time_save_pct > -5:
            status = "⚖️ 基本相当"
        else:
            status = "❌ 同步更优"
        
        print(f"  {size:>3}个节点: 节省{time_save:>4.0f}秒 ({time_save_pct:>5.1f}%) {status}")
    
    print("\n💡 推荐策略:")
    print("  • 小规模(10-100个): 强烈推荐改进异步 (节省37-57%时间)")
    print("  • 中等规模(100-200个): 推荐改进异步 (节省25-28%时间)")
    print("  • 大规模(≥250个): 两种方案都可以 (性能基本相当)")

def main():
    """主函数"""
    print("基于改进异步实验数据生成与同步处理的对比图表...")
    
    # 生成图表
    filename = create_improved_async_vs_sync_charts()
    
    # 打印详细对比
    print_detailed_comparison()
    
    # 打印性能汇总
    print_performance_summary()
    
    print(f"\n实验数据说明:")
    print("- 改进异步数据: 2025年7月24日实际测量 (简化异步处理)")
    print("- 同步数据: 2025年7月24日实际测量")
    print("- 测试环境: Neo4j知识图谱导入")
    print("- 关键改进: 智能并发控制、真正的批处理并发、API限流避免")

if __name__ == "__main__":
    main()
