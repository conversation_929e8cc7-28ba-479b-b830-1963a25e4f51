#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试最新异步处理 - 先测试一个小批次
"""

import asyncio
import json
import logging
import os
import sys
import time
from datetime import datetime
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from neo4j_connection import Neo4jConnection
from simplified_async_import import SimplifiedAsyncImporter

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def quick_test_latest_async():
    """快速测试最新异步处理"""
    neo4j_conn = None
    async_importer = None
    
    try:
        logger.info("开始快速测试最新异步处理...")
        
        # 连接Neo4j
        neo4j_config = Config.get_neo4j_config()
        neo4j_conn = Neo4jConnection(
            uri=neo4j_config["uri"],
            user=neo4j_config["user"],
            password=neo4j_config["password"]
        )
        neo4j_conn.verify_connectivity()
        logger.info("成功连接到Neo4j数据库")
        
        # 创建异步导入器
        async_importer = SimplifiedAsyncImporter(neo4j_conn)
        
        # 读取数据
        json_file_path = os.path.join(os.path.dirname(__file__), "data", "lhasa_knowledge_graph.json")
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        nodes = data.get("nodes", [])
        
        # 去重
        seen_names = set()
        unique_nodes = []
        for node in nodes:
            if node["name"] not in seen_names:
                seen_names.add(node["name"])
                unique_nodes.append(node)
        
        logger.info(f"读取到 {len(nodes)} 个节点，去重后 {len(unique_nodes)} 个节点")
        
        # 测试10个节点
        test_batch_size = 10
        test_nodes = unique_nodes[:test_batch_size]
        
        logger.info(f"开始测试 {test_batch_size} 个节点...")
        
        # 清空数据库
        neo4j_conn.clear_database()
        logger.info("数据库已清空")
        
        # 执行处理
        start_time = time.time()
        result = await async_importer.import_batch_simplified(
            test_nodes, f"quick_test_{test_batch_size}_nodes"
        )
        end_time = time.time()
        
        duration = end_time - start_time
        speed = test_batch_size / duration if duration > 0 else 0
        
        logger.info(f"测试完成:")
        logger.info(f"  - 节点数: {test_batch_size}")
        logger.info(f"  - 成功数: {result.get('success_count', 0)}")
        logger.info(f"  - 失败数: {result.get('failed_count', 0)}")
        logger.info(f"  - 处理时间: {duration:.2f}秒")
        logger.info(f"  - 处理速度: {speed:.3f}节点/秒")
        
        return {
            "batch_size": test_batch_size,
            "success_count": result.get('success_count', 0),
            "failed_count": result.get('failed_count', 0),
            "duration": duration,
            "speed": speed
        }
        
    except Exception as e:
        logger.error(f"测试失败: {e}", exc_info=True)
        return None
    finally:
        if async_importer:
            async_importer.close()


async def test_all_batch_sizes():
    """测试所有批次大小"""
    batch_sizes = [10, 50, 100, 150, 200, 250]
    results = []
    
    for batch_size in batch_sizes:
        logger.info(f"\n{'='*50}")
        logger.info(f"开始测试批次大小: {batch_size}")
        logger.info(f"{'='*50}")
        
        neo4j_conn = None
        async_importer = None
        
        try:
            # 连接Neo4j
            neo4j_config = Config.get_neo4j_config()
            neo4j_conn = Neo4jConnection(
                uri=neo4j_config["uri"],
                user=neo4j_config["user"],
                password=neo4j_config["password"]
            )
            neo4j_conn.verify_connectivity()
            
            # 创建异步导入器
            async_importer = SimplifiedAsyncImporter(neo4j_conn)
            
            # 读取数据
            json_file_path = os.path.join(os.path.dirname(__file__), "data", "lhasa_knowledge_graph.json")
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            nodes = data.get("nodes", [])
            
            # 去重
            seen_names = set()
            unique_nodes = []
            for node in nodes:
                if node["name"] not in seen_names:
                    seen_names.add(node["name"])
                    unique_nodes.append(node)
            
            # 选择节点
            test_nodes = unique_nodes[:batch_size]
            
            # 清空数据库
            neo4j_conn.clear_database()
            logger.info("数据库已清空")
            
            # 执行处理
            start_time = time.time()
            result = await async_importer.import_batch_simplified(
                test_nodes, f"latest_async_{batch_size}_nodes"
            )
            end_time = time.time()
            
            duration = end_time - start_time
            speed = len(test_nodes) / duration if duration > 0 else 0
            
            batch_result = {
                "batch_size": batch_size,
                "actual_nodes": len(test_nodes),
                "success_count": result.get('success_count', 0),
                "failed_count": result.get('failed_count', 0),
                "duration": duration,
                "speed": speed,
                "per_node_time": duration / len(test_nodes) if len(test_nodes) > 0 else 0
            }
            
            results.append(batch_result)
            
            logger.info(f"批次 {batch_size} 完成:")
            logger.info(f"  - 实际节点数: {len(test_nodes)}")
            logger.info(f"  - 成功数: {batch_result['success_count']}")
            logger.info(f"  - 失败数: {batch_result['failed_count']}")
            logger.info(f"  - 处理时间: {duration:.2f}秒 ({duration/60:.2f}分钟)")
            logger.info(f"  - 处理速度: {speed:.3f}节点/秒")
            logger.info(f"  - 每节点耗时: {batch_result['per_node_time']:.2f}秒")
            
        except Exception as e:
            logger.error(f"批次 {batch_size} 失败: {e}", exc_info=True)
            results.append({
                "batch_size": batch_size,
                "error": str(e),
                "success": False
            })
        finally:
            if async_importer:
                async_importer.close()
        
        # 等待一下再进行下一批次
        if batch_size < 250:  # 最后一个批次不需要等待
            logger.info("等待3秒后进行下一批次...")
            await asyncio.sleep(3)
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"latest_async_all_batches_{timestamp}.json"
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        logger.info(f"结果已保存到: {filename}")
    except Exception as e:
        logger.error(f"保存结果失败: {e}")
    
    # 打印汇总
    logger.info(f"\n{'='*60}")
    logger.info("所有批次测试汇总")
    logger.info(f"{'='*60}")
    
    successful_results = [r for r in results if r.get('success', True) and 'error' not in r]
    
    if successful_results:
        logger.info(f"{'批次大小':<8} {'成功率':<8} {'处理时间':<12} {'处理速度':<12}")
        logger.info("-" * 50)
        
        for result in successful_results:
            batch_size = result['batch_size']
            success_rate = result['success_count'] / result['actual_nodes'] * 100 if result['actual_nodes'] > 0 else 0
            duration = result['duration']
            speed = result['speed']
            
            logger.info(f"{batch_size:<8} {success_rate:<8.1f}% {duration:<12.2f}秒 {speed:<12.3f}")
    
    return results


if __name__ == "__main__":
    # 先做快速测试
    logger.info("开始快速测试...")
    quick_result = asyncio.run(quick_test_latest_async())
    
    if quick_result:
        logger.info("快速测试成功，开始完整测试...")
        all_results = asyncio.run(test_all_batch_sizes())
        logger.info("所有测试完成！")
    else:
        logger.error("快速测试失败，停止执行")
