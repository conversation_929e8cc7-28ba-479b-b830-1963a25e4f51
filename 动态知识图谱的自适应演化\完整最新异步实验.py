#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的最新异步实验 - 测试所有批次大小的最新异步处理性能
"""

import asyncio
import json
import logging
import os
import sys
import time
from datetime import datetime
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from neo4j_connection import Neo4jConnection
from simplified_async_import import SimplifiedAsyncImporter

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('complete_latest_async_experiment.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


class CompleteLatestAsyncExperiment:
    """完整的最新异步实验类"""
    
    def __init__(self, neo4j_conn: Neo4jConnection):
        self.neo4j_conn = neo4j_conn
        self.async_importer = SimplifiedAsyncImporter(neo4j_conn)
        
    async def run_complete_latest_async_experiment(self, json_file_path: str, batch_sizes: List[int]):
        """运行完整的最新异步实验"""
        logger.info("="*80)
        logger.info("开始完整的最新异步处理实验")
        logger.info(f"测试批次大小: {batch_sizes}")
        logger.info("="*80)
        
        # 读取数据
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            nodes = data.get("nodes", [])
            logger.info(f"读取到 {len(nodes)} 个节点")
        except Exception as e:
            logger.error(f"读取JSON文件失败: {e}")
            return
        
        # 去重
        seen_names = set()
        unique_nodes = []
        for node in nodes:
            if node["name"] not in seen_names:
                seen_names.add(node["name"])
                unique_nodes.append(node)
        logger.info(f"去重后节点数: {len(unique_nodes)}")
        
        # 实验结果收集
        experiment_results = []
        
        for batch_size in batch_sizes:
            logger.info(f"\n{'='*70}")
            logger.info(f"开始测试批次大小: {batch_size} 个节点")
            logger.info(f"{'='*70}")
            
            try:
                # 清空数据库
                self.neo4j_conn.clear_database()
                logger.info("数据库已清空")
                
                # 选择节点
                if len(unique_nodes) < batch_size:
                    logger.warning(f"节点数不足{batch_size}个，实际只有{len(unique_nodes)}个")
                    batch_nodes = unique_nodes
                else:
                    batch_nodes = unique_nodes[:batch_size]
                
                batch_name = f"latest_async_{batch_size}_nodes"
                
                # 执行最新异步处理
                logger.info(f"开始处理 {len(batch_nodes)} 个节点...")
                start_time = time.time()
                
                result = await self.async_importer.import_batch_simplified(
                    batch_nodes, batch_name
                )
                
                end_time = time.time()
                actual_duration = end_time - start_time
                
                # 更新结果
                result.update({
                    "batch_size": len(batch_nodes),
                    "actual_duration": actual_duration,
                    "actual_speed": len(batch_nodes) / actual_duration if actual_duration > 0 else 0,
                    "processing_type": "latest_async",
                    "timestamp": datetime.now().isoformat()
                })
                
                experiment_results.append(result)
                
                # 记录结果
                logger.info(f"批次 {batch_size} 完成:")
                logger.info(f"  - 实际节点数: {len(batch_nodes)}")
                logger.info(f"  - 成功数: {result.get('success_count', 0)}")
                logger.info(f"  - 失败数: {result.get('failed_count', 0)}")
                logger.info(f"  - 成功率: {result.get('success_count', 0)/len(batch_nodes)*100:.1f}%")
                logger.info(f"  - 处理时间: {actual_duration:.2f}秒 ({actual_duration/60:.2f}分钟)")
                logger.info(f"  - 处理速度: {result['actual_speed']:.3f}节点/秒")
                
                # 验证数据库中的数据
                try:
                    with self.neo4j_conn.driver.session() as session:
                        node_count = session.run("MATCH (n:Attraction) RETURN count(n) as count").single()["count"]
                        logger.info(f"  - 数据库中实际节点数: {node_count}")
                except Exception as e:
                    logger.warning(f"无法验证数据库节点数: {e}")
                
            except Exception as e:
                logger.error(f"批次 {batch_size} 处理失败: {e}", exc_info=True)
                
                # 记录失败结果
                failed_result = {
                    "batch_name": f"latest_async_{batch_size}_nodes_failed",
                    "batch_size": batch_size,
                    "success_count": 0,
                    "failed_count": batch_size,
                    "actual_duration": 0,
                    "actual_speed": 0,
                    "processing_type": "latest_async",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }
                experiment_results.append(failed_result)
                continue
            
            # 批次间等待
            logger.info(f"等待3秒后进行下一批次...")
            await asyncio.sleep(3)
        
        # 保存实验结果
        self.save_experiment_results(experiment_results)
        
        # 打印汇总报告
        self.print_comprehensive_report(experiment_results)
        
        return experiment_results
    
    def save_experiment_results(self, results: List[Dict]):
        """保存实验结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"complete_latest_async_results_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            logger.info(f"实验结果已保存到: {filename}")
        except Exception as e:
            logger.error(f"保存实验结果失败: {e}")
    
    def print_comprehensive_report(self, results: List[Dict]):
        """打印综合报告"""
        logger.info(f"\n{'='*80}")
        logger.info("完整最新异步处理实验汇总报告")
        logger.info(f"{'='*80}")
        
        successful_results = [r for r in results if r.get('success_count', 0) > 0]
        failed_results = [r for r in results if r.get('success_count', 0) == 0]
        
        logger.info(f"成功批次: {len(successful_results)}/{len(results)}")
        logger.info(f"失败批次: {len(failed_results)}/{len(results)}")
        logger.info("")
        
        if successful_results:
            logger.info("成功批次详细结果:")
            logger.info(f"{'批次大小':<8} {'成功率':<8} {'处理时间':<12} {'处理速度':<12} {'每节点耗时':<12}")
            logger.info("-" * 60)
            
            for result in successful_results:
                batch_size = result.get('batch_size', 0)
                success_count = result.get('success_count', 0)
                success_rate = success_count / batch_size * 100 if batch_size > 0 else 0
                duration = result.get('actual_duration', 0)
                speed = result.get('actual_speed', 0)
                per_node_time = duration / batch_size if batch_size > 0 else 0
                
                logger.info(f"{batch_size:<8} {success_rate:<8.1f}% {duration:<12.2f}秒 {speed:<12.3f} {per_node_time:<12.2f}秒")
        
        if failed_results:
            logger.info(f"\n失败批次:")
            for result in failed_results:
                batch_size = result.get('batch_size', 0)
                error = result.get('error', 'Unknown error')
                logger.info(f"  - {batch_size}个节点: {error}")
        
        # 性能统计
        if successful_results:
            total_nodes = sum(r.get('success_count', 0) for r in successful_results)
            total_time = sum(r.get('actual_duration', 0) for r in successful_results)
            avg_speed = sum(r.get('actual_speed', 0) for r in successful_results) / len(successful_results)
            
            logger.info(f"\n📊 整体统计:")
            logger.info(f"总处理节点数: {total_nodes}")
            logger.info(f"总处理时间: {total_time:.2f}秒 ({total_time/60:.2f}分钟)")
            logger.info(f"整体平均速度: {total_nodes/total_time:.3f}节点/秒" if total_time > 0 else "N/A")
            logger.info(f"各批次平均速度: {avg_speed:.3f}节点/秒")
        
        logger.info(f"{'='*80}")
    
    def close(self):
        """关闭连接"""
        if hasattr(self, 'async_importer'):
            self.async_importer.close()


async def main():
    """主函数"""
    experiment = None
    try:
        # 连接Neo4j
        neo4j_config = Config.get_neo4j_config()
        neo4j_conn = Neo4jConnection(
            uri=neo4j_config["uri"],
            user=neo4j_config["user"],
            password=neo4j_config["password"]
        )
        neo4j_conn.verify_connectivity()
        logger.info("成功连接到Neo4j数据库")
        
        # 创建实验器
        experiment = CompleteLatestAsyncExperiment(neo4j_conn)
        
        # 设置测试批次大小 - 所有批次
        batch_sizes = [10, 50, 100, 150, 200, 250]
        
        # JSON文件路径
        json_file_path = os.path.join(os.path.dirname(__file__), "data", "lhasa_knowledge_graph.json")
        
        # 运行完整的最新异步实验
        results = await experiment.run_complete_latest_async_experiment(json_file_path, batch_sizes)
        
        logger.info("完整最新异步处理实验完成！")
        
    except Exception as e:
        logger.error(f"主程序错误: {e}", exc_info=True)
    finally:
        if experiment:
            experiment.close()


if __name__ == "__main__":
    asyncio.run(main())
