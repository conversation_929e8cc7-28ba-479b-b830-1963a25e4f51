#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重现原图布局，仅修改颜色提高对比度
"""

import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300

# 高对比度颜色方案
colors = {
    'async_blue': '#0066CC',      # 深蓝色 - 异步处理
    'sync_orange': '#FF6600',     # 橙色 - 同步处理  
    'improvement_green': '#009900', # 绿色 - 改进效果
    'async_line': '#003399',      # 深蓝线条
    'sync_line': '#CC3300',       # 深红线条
    'improvement_line': '#006600'  # 深绿线条
}

# 原图数据
data = {
    'batch_sizes': [10, 50, 100, 150, 200, 250],
    'async_times': [70.50, 238.30, 761.39, 1177.43, 1366.78, 1456.84],
    'sync_times': [118, 556, 1207, 1569, 1909, 2167],
    'async_speeds': [0.142, 0.210, 0.131, 0.127, 0.146, 0.172],
    'sync_speeds': [0.085, 0.090, 0.083, 0.096, 0.105, 0.115],
    'improvements': [40.3, 57.1, 36.9, 25.0, 28.4, 32.8]
}

def create_original_chart_with_new_colors():
    """重现原图，仅修改颜色"""
    
    # 创建2x3布局，与原图相同
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    fig.patch.set_facecolor('white')
    
    # 1. 处理时间对比 (左上)
    ax1 = axes[0, 0]
    x = np.arange(len(data['batch_sizes']))
    width = 0.35
    
    ax1.bar(x - width/2, data['async_times'], width, 
            label='异步处理', color=colors['async_blue'], alpha=0.8)
    ax1.bar(x + width/2, data['sync_times'], width,
            label='同步处理', color=colors['sync_orange'], alpha=0.8)
    
    ax1.set_xlabel('批次大小 (个节点)')
    ax1.set_ylabel('处理时间 (秒)')
    ax1.set_title('处理时间对比 (含节点数量)')
    ax1.set_xticks(x)
    ax1.set_xticklabels(data['batch_sizes'])
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 处理速度对比 (中上)
    ax2 = axes[0, 1]
    ax2.plot(data['batch_sizes'], data['async_speeds'], 
             marker='o', linewidth=2, color=colors['async_line'], 
             label='异步处理速度', markersize=6)
    ax2.plot(data['batch_sizes'], data['sync_speeds'], 
             marker='s', linewidth=2, color=colors['sync_line'],
             label='同步处理速度', markersize=6)
    
    ax2.set_xlabel('批次大小 (个节点)')
    ax2.set_ylabel('处理速度 (节点/秒)')
    ax2.set_title('处理速度对比 (含节点数量)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 性能提升百分比 (右上)
    ax3 = axes[0, 2]
    bars = ax3.bar(data['batch_sizes'], data['improvements'], 
                   color=colors['improvement_green'], alpha=0.8)
    
    ax3.set_xlabel('批次大小 (个节点)')
    ax3.set_ylabel('性能提升百分比 (%)')
    ax3.set_title('异步处理性能提升幅度')
    ax3.grid(True, alpha=0.3)
    
    # 添加百分比标签
    for bar in bars:
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{height:.1f}%', ha='center', va='bottom', fontsize=9)
    
    # 4. 单节点处理时间 (左下)
    ax4 = axes[1, 0]
    async_per_node = [t/s for t, s in zip(data['async_times'], data['batch_sizes'])]
    sync_per_node = [t/s for t, s in zip(data['sync_times'], data['batch_sizes'])]
    
    ax4.plot(data['batch_sizes'], async_per_node, 
             marker='o', linewidth=2, color=colors['async_line'],
             label='异步处理每节点时间', markersize=6)
    ax4.plot(data['batch_sizes'], sync_per_node, 
             marker='s', linewidth=2, color=colors['sync_line'],
             label='同步处理每节点时间', markersize=6)
    
    ax4.set_xlabel('批次大小 (个节点)')
    ax4.set_ylabel('每节点处理时间 (秒)')
    ax4.set_title('单节点处理时间对比')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    # 5. 累积处理时间 (中下)
    ax5 = axes[1, 1]
    cumulative_async = np.cumsum(data['async_times'])
    cumulative_sync = np.cumsum(data['sync_times'])
    
    ax5.plot(data['batch_sizes'], cumulative_async, 
             marker='o', linewidth=2, color=colors['async_line'],
             label='异步累积时间', markersize=6)
    ax5.plot(data['batch_sizes'], cumulative_sync, 
             marker='s', linewidth=2, color=colors['sync_line'],
             label='同步累积时间', markersize=6)
    
    ax5.set_xlabel('批次大小 (个节点)')
    ax5.set_ylabel('累积处理时间 (秒)')
    ax5.set_title('累积处理时间对比')
    ax5.legend()
    ax5.grid(True, alpha=0.3)
    
    # 6. 综合性能雷达图 (右下)
    ax6 = axes[1, 2]
    ax6 = plt.subplot(2, 3, 6, projection='polar')
    
    categories = ['处理速度', '时间效率', '资源利用', '扩展性', '稳定性']
    async_scores = [0.8, 0.9, 0.85, 0.8, 1.0]
    sync_scores = [0.6, 0.6, 0.7, 0.9, 0.9]
    
    angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
    async_scores += async_scores[:1]
    sync_scores += sync_scores[:1]
    angles += angles[:1]
    
    ax6.plot(angles, async_scores, 'o-', linewidth=2, 
             color=colors['async_line'], label='异步处理')
    ax6.fill(angles, async_scores, alpha=0.25, color=colors['async_blue'])
    
    ax6.plot(angles, sync_scores, 's-', linewidth=2,
             color=colors['sync_line'], label='同步处理')
    ax6.fill(angles, sync_scores, alpha=0.25, color=colors['sync_orange'])
    
    ax6.set_xticks(angles[:-1])
    ax6.set_xticklabels(categories)
    ax6.set_ylim(0, 1)
    ax6.set_title('综合性能雷达图', pad=20)
    ax6.legend(loc='upper right', bbox_to_anchor=(1.2, 1.0))
    ax6.grid(True, alpha=0.3)
    
    # 调整布局
    plt.tight_layout()
    
    # 添加总标题
    fig.suptitle('异步处理 vs 同步处理 - 完整性能对比分析 (高对比度版本)', 
                fontsize=14, fontweight='bold', y=0.98)
    
    # 保存图表
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f'原图高对比度版本_{timestamp}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    print(f"高对比度版本已保存为: {filename}")
    plt.close()
    
    return filename

if __name__ == "__main__":
    create_original_chart_with_new_colors()
