#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门测试250个节点的实验 - 验证修复后的同步处理
"""

import asyncio
import json
import logging
import os
import sys
import time
from datetime import datetime
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from neo4j_connection import Neo4jConnection
from adaptive_processing_strategy import AdaptiveProcessingStrategy

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_250_nodes.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


class Test250NodesExperiment:
    """专门测试250个节点的实验类"""
    
    def __init__(self, neo4j_conn: Neo4jConnection):
        self.neo4j_conn = neo4j_conn
        self.adaptive_strategy = AdaptiveProcessingStrategy(neo4j_conn)
        
    async def test_250_nodes_comprehensive(self, json_file_path: str):
        """全面测试250个节点的处理"""
        logger.info("="*80)
        logger.info("开始250个节点的全面测试")
        logger.info("="*80)
        
        # 读取数据
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            nodes = data.get("nodes", [])
            logger.info(f"读取到 {len(nodes)} 个节点")
        except Exception as e:
            logger.error(f"读取JSON文件失败: {e}")
            return
        
        # 去重
        seen_names = set()
        unique_nodes = []
        for node in nodes:
            if node["name"] not in seen_names:
                seen_names.add(node["name"])
                unique_nodes.append(node)
        logger.info(f"去重后节点数: {len(unique_nodes)}")
        
        if len(unique_nodes) < 250:
            logger.warning(f"节点数不足250个，实际只有{len(unique_nodes)}个")
            batch_nodes = unique_nodes
        else:
            batch_nodes = unique_nodes[:250]
        
        logger.info(f"将测试 {len(batch_nodes)} 个节点")
        
        # 测试结果收集
        test_results = []
        
        # 1. 测试自适应策略选择
        logger.info("\n" + "="*60)
        logger.info("测试1: 自适应策略选择")
        logger.info("="*60)
        
        try:
            # 清空数据库
            self.neo4j_conn.clear_database()
            logger.info("数据库已清空")
            
            # 执行自适应处理
            result = await self.adaptive_strategy.process_batch_adaptive(
                batch_nodes, "adaptive_250_nodes_test"
            )
            
            test_results.append({
                "test_name": "adaptive_strategy",
                "result": result,
                "success": True
            })
            
            logger.info("自适应策略测试完成:")
            logger.info(f"  - 选择策略: {result.get('strategy_used', 'unknown')}")
            logger.info(f"  - 成功数: {result.get('success_count', 0)}/{result.get('total_nodes', 0)}")
            logger.info(f"  - 耗时: {result.get('actual_time', 0):.2f}秒")
            logger.info(f"  - 速度: {result.get('actual_speed', 0):.3f}节点/秒")
            
        except Exception as e:
            logger.error(f"自适应策略测试失败: {e}", exc_info=True)
            test_results.append({
                "test_name": "adaptive_strategy",
                "result": None,
                "success": False,
                "error": str(e)
            })
        
        # 2. 强制测试异步处理
        logger.info("\n" + "="*60)
        logger.info("测试2: 强制异步处理")
        logger.info("="*60)
        
        try:
            # 清空数据库
            self.neo4j_conn.clear_database()
            logger.info("数据库已清空")
            
            # 强制使用异步处理
            result = await self.adaptive_strategy.async_importer.import_batch_simplified(
                batch_nodes, "forced_async_250_nodes"
            )
            
            test_results.append({
                "test_name": "forced_async",
                "result": result,
                "success": True
            })
            
            logger.info("强制异步处理测试完成:")
            logger.info(f"  - 成功数: {result.get('success_count', 0)}/{result.get('total_nodes', 0)}")
            logger.info(f"  - 耗时: {result.get('duration_seconds', 0):.2f}秒")
            logger.info(f"  - 速度: {result.get('nodes_per_second', 0):.3f}节点/秒")
            
        except Exception as e:
            logger.error(f"强制异步处理测试失败: {e}", exc_info=True)
            test_results.append({
                "test_name": "forced_async",
                "result": None,
                "success": False,
                "error": str(e)
            })
        
        # 3. 强制测试同步处理
        logger.info("\n" + "="*60)
        logger.info("测试3: 强制同步处理")
        logger.info("="*60)
        
        try:
            # 清空数据库
            self.neo4j_conn.clear_database()
            logger.info("数据库已清空")
            
            # 强制使用同步处理
            result = await asyncio.to_thread(
                self.adaptive_strategy.sync_importer.import_batch_sync,
                batch_nodes, "forced_sync_250_nodes"
            )
            
            test_results.append({
                "test_name": "forced_sync",
                "result": result,
                "success": True
            })
            
            logger.info("强制同步处理测试完成:")
            logger.info(f"  - 成功数: {result.get('success_count', 0)}/{result.get('total_nodes', 0)}")
            logger.info(f"  - 耗时: {result.get('duration_seconds', 0):.2f}秒")
            logger.info(f"  - 速度: {result.get('nodes_per_second', 0):.3f}节点/秒")
            
        except Exception as e:
            logger.error(f"强制同步处理测试失败: {e}", exc_info=True)
            test_results.append({
                "test_name": "forced_sync",
                "result": None,
                "success": False,
                "error": str(e)
            })
        
        # 保存测试结果
        self.save_test_results(test_results)
        
        # 打印汇总报告
        self.print_comprehensive_report(test_results)
        
        return test_results
    
    def save_test_results(self, results: List[Dict]):
        """保存测试结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"test_250_nodes_results_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            logger.info(f"测试结果已保存到: {filename}")
        except Exception as e:
            logger.error(f"保存测试结果失败: {e}")
    
    def print_comprehensive_report(self, results: List[Dict]):
        """打印综合报告"""
        logger.info("\n" + "="*80)
        logger.info("250个节点测试综合报告")
        logger.info("="*80)
        
        for test_result in results:
            test_name = test_result["test_name"]
            success = test_result["success"]
            
            if success:
                result = test_result["result"]
                success_count = result.get("success_count", 0)
                total_nodes = result.get("total_nodes", 0)
                duration = result.get("duration_seconds", result.get("actual_time", 0))
                speed = result.get("nodes_per_second", result.get("actual_speed", 0))
                success_rate = (success_count / total_nodes * 100) if total_nodes > 0 else 0
                
                strategy = result.get("strategy_used", "unknown")
                
                logger.info(f"\n📊 {test_name.upper()} 测试结果:")
                logger.info(f"  策略: {strategy}")
                logger.info(f"  成功率: {success_count}/{total_nodes} ({success_rate:.1f}%)")
                logger.info(f"  处理时间: {duration:.2f}秒")
                logger.info(f"  处理速度: {speed:.3f}节点/秒")
                
                if success_count == total_nodes:
                    logger.info(f"  状态: ✅ 完全成功")
                elif success_count > 0:
                    logger.info(f"  状态: ⚠️ 部分成功")
                else:
                    logger.info(f"  状态: ❌ 完全失败")
            else:
                error = test_result.get("error", "Unknown error")
                logger.info(f"\n❌ {test_name.upper()} 测试失败:")
                logger.info(f"  错误: {error}")
        
        # 性能对比
        logger.info("\n" + "="*60)
        logger.info("性能对比分析")
        logger.info("="*60)
        
        successful_tests = [r for r in results if r["success"]]
        if len(successful_tests) >= 2:
            # 找出最快和最慢的
            speeds = []
            for test in successful_tests:
                result = test["result"]
                speed = result.get("nodes_per_second", result.get("actual_speed", 0))
                speeds.append((test["test_name"], speed, result.get("success_count", 0)))
            
            speeds.sort(key=lambda x: x[1], reverse=True)
            
            logger.info("处理速度排名:")
            for i, (name, speed, success_count) in enumerate(speeds):
                rank_icon = "🥇" if i == 0 else "🥈" if i == 1 else "🥉"
                logger.info(f"  {rank_icon} {name}: {speed:.3f}节点/秒 (成功{success_count}个)")
        
        logger.info("\n" + "="*80)
        logger.info("测试完成！")
        logger.info("="*80)
    
    def close(self):
        """关闭连接"""
        if hasattr(self, 'adaptive_strategy'):
            self.adaptive_strategy.close()


async def main():
    """主函数"""
    tester = None
    try:
        # 连接Neo4j
        neo4j_config = Config.get_neo4j_config()
        neo4j_conn = Neo4jConnection(
            uri=neo4j_config["uri"],
            user=neo4j_config["user"],
            password=neo4j_config["password"]
        )
        neo4j_conn.verify_connectivity()
        logger.info("成功连接到Neo4j数据库")
        
        # 创建测试器
        tester = Test250NodesExperiment(neo4j_conn)
        
        # JSON文件路径
        json_file_path = os.path.join(os.path.dirname(__file__), "data", "lhasa_knowledge_graph.json")
        
        # 运行250个节点的全面测试
        results = await tester.test_250_nodes_comprehensive(json_file_path)
        
        logger.info("250个节点测试实验完成！")
        
    except Exception as e:
        logger.error(f"主程序错误: {e}", exc_info=True)
    finally:
        if tester:
            tester.close()


if __name__ == "__main__":
    asyncio.run(main())
