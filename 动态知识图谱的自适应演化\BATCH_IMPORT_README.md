# 批次导入性能对比实验

这个实验用于对比异步和同步方式导入Neo4j数据库的性能差异。

## 文件说明

- `batch_async_import.py` - 异步批次导入脚本
- `batch_sync_import.py` - 同步批次导入脚本  
- `performance_comparison.py` - 性能对比分析脚本
- `run_batch_experiments.py` - 一键运行所有实验的主脚本

## 实验设计

### 批次大小
按照以下分组进行实验：
- 10个景点
- 50个景点
- 100个景点
- 150个景点
- 200个景点
- 250个景点

### 测量指标
- 导入耗时（秒）
- 导入速度（节点/秒）
- 成功率（%）
- 内存使用情况

## 使用方法

### 1. 运行完整实验（推荐）
```bash
cd 动态知识图谱的自适应演化
python run_batch_experiments.py
```

### 2. 单独运行异步实验
```bash
python run_batch_experiments.py --type async
```

### 3. 单独运行同步实验
```bash
python run_batch_experiments.py --type sync
```

### 4. 只生成对比报告（需要先有实验结果）
```bash
python run_batch_experiments.py --type compare
```

### 5. 自定义批次大小
```bash
python run_batch_experiments.py --sizes 20 100 200
```

### 6. 直接运行单个脚本
```bash
# 异步导入
python batch_async_import.py

# 同步导入
python batch_sync_import.py

# 性能对比
python performance_comparison.py
```

## 输出文件

### 日志文件
- `batch_experiments.log` - 主实验日志
- `batch_async_import.log` - 异步导入日志
- `batch_sync_import.log` - 同步导入日志

### 结果文件
- `async_batch_import_results_YYYYMMDD_HHMMSS.json` - 异步实验结果
- `sync_batch_import_results_YYYYMMDD_HHMMSS.json` - 同步实验结果
- `performance_comparison_YYYYMMDD_HHMMSS.json` - 性能对比数据

### 图表文件
- `performance_comparison_charts_YYYYMMDD_HHMMSS.png` - 性能对比图表

## 实验结果示例

```
异步 vs 同步导入性能对比报告
================================================================================
批次大小   异步耗时     同步耗时     时间提升     异步速度     同步速度     速度提升    
--------------------------------------------------------------------------------
10       12.34      15.67      21.2%      0.81       0.64       26.6%
50       45.23      62.18      27.3%      1.11       0.80       38.8%
100      89.45      125.67     28.8%      1.12       0.80       40.0%
150      134.56     189.23     28.9%      1.11       0.79       40.5%
200      178.90     251.45     28.8%      1.12       0.80       40.0%
250      223.45     314.67     29.0%      1.12       0.79       41.8%
--------------------------------------------------------------------------------
平均时间提升: 27.3%
平均速度提升: 37.9%
================================================================================
```

## 注意事项

1. **Neo4j连接**: 确保Neo4j数据库正在运行，连接配置正确
2. **数据文件**: 确保`data/lhasa_knowledge_graph.json`文件存在
3. **依赖包**: 需要安装matplotlib和pandas用于图表生成
4. **数据库清理**: 每次实验前会自动清空数据库
5. **实验时间**: 完整实验可能需要较长时间，请耐心等待

## 依赖安装

```bash
pip install matplotlib pandas
```

## 故障排除

### 1. Neo4j连接失败
- 检查Neo4j服务是否启动
- 验证连接配置（URI、用户名、密码）
- 确认防火墙设置

### 2. 内存不足
- 减少批次大小
- 增加系统内存
- 调整Neo4j内存配置

### 3. 导入失败
- 检查JSON数据格式
- 查看详细错误日志
- 验证数据完整性

## 实验原理

### 异步导入优势
1. **并发处理**: 同时处理多个节点
2. **I/O优化**: 减少等待时间
3. **资源利用**: 更好的CPU和网络利用率

### 同步导入特点
1. **顺序处理**: 逐个处理节点
2. **简单可靠**: 错误处理更直观
3. **内存友好**: 内存使用更稳定

通过这个实验，你可以量化异步和同步导入的性能差异，为实际项目选择合适的导入策略提供数据支持。
