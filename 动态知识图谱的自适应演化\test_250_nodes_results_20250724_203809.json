[{"test_name": "adaptive_strategy", "result": {"batch_name": "adaptive_250_nodes_test", "total_nodes": 250, "success_count": 0, "failed_count": 250, "duration_seconds": 3.107663869857788, "start_time": "2025-07-24T20:13:47.938506", "end_time": "2025-07-24T20:13:51.046170", "nodes_per_second": 80.44628070134252, "strategy_used": "sync", "strategy_reason": "同步处理在此批次大小下更优", "actual_time": 3.1096811294555664, "actual_speed": 80.39409495460689, "batch_size": 250}, "success": true}, {"test_name": "forced_async", "result": {"batch_name": "forced_async_250_nodes", "total_nodes": 250, "success_count": 250, "failed_count": 0, "duration_seconds": 1456.8398900032043, "start_time": "2025-07-24T20:13:51.054726", "end_time": "2025-07-24T20:38:07.894616", "nodes_per_second": 0.1716043071826171, "improvement_type": "simplified_async"}, "success": true}, {"test_name": "forced_sync", "result": {"batch_name": "forced_sync_250_nodes", "total_nodes": 250, "success_count": 0, "failed_count": 250, "duration_seconds": 1.7797064781188965, "start_time": "2025-07-24T20:38:07.978237", "end_time": "2025-07-24T20:38:09.757943", "nodes_per_second": 140.47260212495462}, "success": true}]