#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
混合处理引擎 - 结合多种优化策略的智能处理系统
"""

import asyncio
import json
import logging
import os
import sys
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from neo4j_connection import Neo4jConnection
from adaptive_processing_strategy import AdaptiveProcessingStrategy
from dynamic_batch_optimizer import DynamicBatchOptimizer, AdaptiveBatchProcessor, PerformanceMetrics

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('hybrid_processing.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


class ProcessingMode(Enum):
    """处理模式枚举"""
    ADAPTIVE_STRATEGY = "adaptive_strategy"      # 自适应策略选择
    DYNAMIC_BATCHING = "dynamic_batching"        # 动态批次优化
    HYBRID_OPTIMAL = "hybrid_optimal"            # 混合最优模式


@dataclass
class ProcessingConfig:
    """处理配置"""
    mode: ProcessingMode
    initial_batch_size: int = 20
    max_concurrent_batches: int = 3
    api_rate_limit: int = 8
    enable_performance_monitoring: bool = True
    enable_auto_scaling: bool = True
    fallback_to_sync: bool = True


class HybridProcessingEngine:
    """混合处理引擎 - 智能选择和组合多种处理策略"""
    
    def __init__(self, neo4j_conn: Neo4jConnection, config: ProcessingConfig):
        self.neo4j_conn = neo4j_conn
        self.config = config
        
        # 初始化各种处理器
        self.adaptive_strategy = AdaptiveProcessingStrategy(neo4j_conn)
        self.batch_optimizer = DynamicBatchOptimizer(
            initial_batch_size=config.initial_batch_size,
            min_batch_size=5,
            max_batch_size=50
        )
        
        # 性能监控
        self.performance_history = []
        self.processing_stats = {
            "total_processed": 0,
            "total_time": 0.0,
            "strategy_usage": {},
            "optimization_events": []
        }
        
    async def process_intelligent(self, nodes: List[Dict], batch_name: str) -> Dict:
        """智能处理 - 根据配置和实时情况选择最优策略"""
        total_nodes = len(nodes)
        logger.info(f"开始智能处理: {batch_name}, 节点数: {total_nodes}, 模式: {self.config.mode.value}")
        
        start_time = time.time()
        
        try:
            if self.config.mode == ProcessingMode.ADAPTIVE_STRATEGY:
                # 使用自适应策略选择
                result = await self._process_with_adaptive_strategy(nodes, batch_name)
                
            elif self.config.mode == ProcessingMode.DYNAMIC_BATCHING:
                # 使用动态批次优化
                result = await self._process_with_dynamic_batching(nodes, batch_name)
                
            elif self.config.mode == ProcessingMode.HYBRID_OPTIMAL:
                # 使用混合最优模式
                result = await self._process_with_hybrid_optimal(nodes, batch_name)
                
            else:
                raise ValueError(f"不支持的处理模式: {self.config.mode}")
            
            # 记录性能统计
            end_time = time.time()
            duration = end_time - start_time
            
            self._update_performance_stats(result, duration)
            
            result.update({
                "processing_mode": self.config.mode.value,
                "total_duration": duration,
                "engine_type": "hybrid_intelligent"
            })
            
            logger.info(f"智能处理完成: {batch_name}, 耗时: {duration:.2f}秒")
            return result
            
        except Exception as e:
            logger.error(f"智能处理失败: {e}", exc_info=True)
            
            # 尝试降级处理
            if self.config.fallback_to_sync:
                logger.info("尝试降级到同步处理...")
                return await self._fallback_sync_processing(nodes, batch_name)
            else:
                raise
    
    async def _process_with_adaptive_strategy(self, nodes: List[Dict], batch_name: str) -> Dict:
        """使用自适应策略处理"""
        logger.info("使用自适应策略选择处理方式")
        
        # 根据节点数量选择最优策略
        strategy = self.adaptive_strategy.choose_optimal_strategy(len(nodes))
        
        if strategy == "async":
            result = await self.adaptive_strategy.async_importer.import_batch_simplified(nodes, batch_name)
        else:
            result = await asyncio.to_thread(
                self.adaptive_strategy.sync_importer.import_batch_sync, nodes, batch_name
            )
        
        result["strategy_used"] = strategy
        result["selection_reason"] = f"自适应策略选择了{strategy}处理"
        
        # 更新策略使用统计
        self.processing_stats["strategy_usage"][strategy] = \
            self.processing_stats["strategy_usage"].get(strategy, 0) + 1
        
        return result
    
    async def _process_with_dynamic_batching(self, nodes: List[Dict], batch_name: str) -> Dict:
        """使用动态批次优化处理"""
        logger.info("使用动态批次优化处理")
        
        # 创建自适应批处理器
        async def async_processor(batch_nodes, batch_name):
            return await self.adaptive_strategy.async_importer.import_batch_simplified(batch_nodes, batch_name)
        
        adaptive_processor = AdaptiveBatchProcessor(async_processor, self.config.initial_batch_size)
        
        result = await adaptive_processor.process_with_adaptive_batching(nodes, batch_name)
        result["optimization_summary"] = adaptive_processor.optimizer.get_optimization_summary()
        
        return result
    
    async def _process_with_hybrid_optimal(self, nodes: List[Dict], batch_name: str) -> Dict:
        """使用混合最优模式处理"""
        logger.info("使用混合最优模式处理")
        
        total_nodes = len(nodes)
        
        # 第一阶段：策略选择
        base_strategy = self.adaptive_strategy.choose_optimal_strategy(total_nodes)
        logger.info(f"基础策略选择: {base_strategy}")
        
        # 第二阶段：根据策略进行动态批次优化
        if base_strategy == "async" and total_nodes > 50:
            # 对于异步处理且节点数较多的情况，使用动态批次优化
            logger.info("应用动态批次优化到异步处理")
            
            async def optimized_async_processor(batch_nodes, batch_name):
                return await self.adaptive_strategy.async_importer.import_batch_simplified(batch_nodes, batch_name)
            
            adaptive_processor = AdaptiveBatchProcessor(optimized_async_processor, self.config.initial_batch_size)
            result = await adaptive_processor.process_with_adaptive_batching(nodes, batch_name)
            
            result.update({
                "base_strategy": base_strategy,
                "optimization_applied": "dynamic_batching",
                "optimization_summary": adaptive_processor.optimizer.get_optimization_summary()
            })
            
        elif base_strategy == "sync" and total_nodes > 100:
            # 对于同步处理且节点数很多的情况，使用分段并行
            logger.info("应用分段并行优化到同步处理")
            result = await self._process_sync_with_segmentation(nodes, batch_name)
            
            result.update({
                "base_strategy": base_strategy,
                "optimization_applied": "segmented_parallel"
            })
            
        else:
            # 直接使用基础策略
            logger.info(f"直接使用基础策略: {base_strategy}")
            
            if base_strategy == "async":
                result = await self.adaptive_strategy.async_importer.import_batch_simplified(nodes, batch_name)
            else:
                result = await asyncio.to_thread(
                    self.adaptive_strategy.sync_importer.import_batch_sync, nodes, batch_name
                )
            
            result.update({
                "base_strategy": base_strategy,
                "optimization_applied": "none"
            })
        
        return result
    
    async def _process_sync_with_segmentation(self, nodes: List[Dict], batch_name: str) -> Dict:
        """同步处理的分段并行优化"""
        total_nodes = len(nodes)
        segment_size = min(100, max(50, total_nodes // 4))  # 每段50-100个节点
        
        segments = [nodes[i:i + segment_size] for i in range(0, total_nodes, segment_size)]
        logger.info(f"分成 {len(segments)} 段进行并行同步处理，每段约 {segment_size} 个节点")
        
        start_time = time.time()
        
        # 限制并发段数
        semaphore = asyncio.Semaphore(min(3, len(segments)))
        
        async def process_segment(segment, segment_idx):
            async with semaphore:
                segment_name = f"{batch_name}_segment_{segment_idx}"
                return await asyncio.to_thread(
                    self.adaptive_strategy.sync_importer.import_batch_sync, segment, segment_name
                )
        
        # 并发处理所有段
        tasks = [process_segment(segment, i) for i, segment in enumerate(segments)]
        segment_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 汇总结果
        total_success = 0
        total_failed = 0
        
        for i, segment_result in enumerate(segment_results):
            if isinstance(segment_result, Exception):
                logger.error(f"段 {i+1} 处理失败: {segment_result}")
                total_failed += len(segments[i])
            else:
                total_success += segment_result.get('success_count', 0)
                total_failed += segment_result.get('failed_count', 0)
        
        end_time = time.time()
        duration = end_time - start_time
        
        return {
            "batch_name": batch_name,
            "total_nodes": total_nodes,
            "success_count": total_success,
            "failed_count": total_failed,
            "duration_seconds": duration,
            "nodes_per_second": total_nodes / duration if duration > 0 else 0,
            "segments_count": len(segments),
            "segment_size": segment_size
        }
    
    async def _fallback_sync_processing(self, nodes: List[Dict], batch_name: str) -> Dict:
        """降级同步处理"""
        logger.warning("执行降级同步处理")
        
        try:
            result = await asyncio.to_thread(
                self.adaptive_strategy.sync_importer.import_batch_sync, nodes, f"{batch_name}_fallback"
            )
            result["fallback_used"] = True
            result["fallback_reason"] = "主要处理策略失败，降级到同步处理"
            return result
        except Exception as e:
            logger.error(f"降级处理也失败: {e}")
            raise
    
    def _update_performance_stats(self, result: Dict, duration: float):
        """更新性能统计"""
        self.processing_stats["total_processed"] += result.get("total_nodes", 0)
        self.processing_stats["total_time"] += duration
        
        # 记录性能历史
        performance_record = {
            "timestamp": datetime.now().isoformat(),
            "batch_name": result.get("batch_name", "unknown"),
            "total_nodes": result.get("total_nodes", 0),
            "success_count": result.get("success_count", 0),
            "duration": duration,
            "nodes_per_second": result.get("nodes_per_second", 0),
            "strategy_used": result.get("strategy_used", "unknown"),
            "processing_mode": self.config.mode.value
        }
        
        self.performance_history.append(performance_record)
        
        # 保留最近50条记录
        if len(self.performance_history) > 50:
            self.performance_history = self.performance_history[-50:]
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能总结"""
        if not self.performance_history:
            return {"status": "无性能数据"}
        
        recent_records = self.performance_history[-10:]  # 最近10次
        
        avg_speed = sum(r["nodes_per_second"] for r in recent_records) / len(recent_records)
        avg_success_rate = sum(r["success_count"] / r["total_nodes"] for r in recent_records if r["total_nodes"] > 0) / len(recent_records)
        
        total_processed = self.processing_stats["total_processed"]
        total_time = self.processing_stats["total_time"]
        overall_speed = total_processed / total_time if total_time > 0 else 0
        
        return {
            "overall_stats": {
                "total_processed": total_processed,
                "total_time": total_time,
                "overall_speed": overall_speed
            },
            "recent_performance": {
                "avg_speed": avg_speed,
                "avg_success_rate": avg_success_rate,
                "records_count": len(recent_records)
            },
            "strategy_usage": self.processing_stats["strategy_usage"],
            "processing_mode": self.config.mode.value
        }
    
    async def run_comprehensive_experiment(self, json_file_path: str, batch_sizes: List[int]):
        """运行综合实验"""
        logger.info(f"开始混合处理引擎综合实验，批次大小: {batch_sizes}")
        
        # 读取数据
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            nodes = data.get("nodes", [])
            logger.info(f"读取到 {len(nodes)} 个节点")
        except Exception as e:
            logger.error(f"读取JSON文件失败: {e}")
            return
        
        # 去重
        seen_names = set()
        unique_nodes = []
        for node in nodes:
            if node["name"] not in seen_names:
                seen_names.add(node["name"])
                unique_nodes.append(node)
        logger.info(f"去重后节点数: {len(unique_nodes)}")
        
        # 实验结果
        experiment_results = []
        
        for batch_size in batch_sizes:
            logger.info(f"\n{'='*70}")
            logger.info(f"开始混合处理实验 - 批次大小: {batch_size}")
            logger.info(f"{'='*70}")
            
            # 清空数据库
            try:
                self.neo4j_conn.clear_database()
                logger.info("数据库已清空")
            except Exception as e:
                logger.error(f"清空数据库失败: {e}")
                continue
            
            # 选择节点
            batch_nodes = unique_nodes[:batch_size]
            batch_name = f"hybrid_experiment_{batch_size}_nodes"
            
            # 执行混合处理
            try:
                result = await self.process_intelligent(batch_nodes, batch_name)
                experiment_results.append(result)
                
                # 记录结果
                logger.info(f"批次 {batch_size} 结果:")
                logger.info(f"  - 处理模式: {result.get('processing_mode', 'unknown')}")
                logger.info(f"  - 策略: {result.get('strategy_used', result.get('base_strategy', 'unknown'))}")
                logger.info(f"  - 成功: {result.get('success_count', 0)}/{result.get('total_nodes', batch_size)}")
                logger.info(f"  - 耗时: {result.get('total_duration', 0):.2f} 秒")
                logger.info(f"  - 速度: {result.get('nodes_per_second', 0):.3f} 节点/秒")
                
            except Exception as e:
                logger.error(f"批次 {batch_size} 执行失败: {e}")
                continue
            
            # 等待间隔
            await asyncio.sleep(3)
        
        # 保存结果
        self.save_experiment_results(experiment_results)
        
        # 打印总结
        self.print_comprehensive_summary(experiment_results)
        
        return experiment_results
    
    def save_experiment_results(self, results: List[Dict]):
        """保存实验结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"hybrid_processing_results_{timestamp}.json"
        
        try:
            # 添加性能总结
            comprehensive_results = {
                "experiment_results": results,
                "performance_summary": self.get_performance_summary(),
                "config": {
                    "mode": self.config.mode.value,
                    "initial_batch_size": self.config.initial_batch_size,
                    "max_concurrent_batches": self.config.max_concurrent_batches
                },
                "timestamp": timestamp
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(comprehensive_results, f, ensure_ascii=False, indent=2)
            logger.info(f"综合实验结果已保存到: {filename}")
        except Exception as e:
            logger.error(f"保存实验结果失败: {e}")
    
    def print_comprehensive_summary(self, results: List[Dict]):
        """打印综合总结"""
        logger.info(f"\n{'='*80}")
        logger.info("混合处理引擎综合实验总结")
        logger.info(f"{'='*80}")
        
        performance_summary = self.get_performance_summary()
        
        logger.info(f"处理模式: {self.config.mode.value}")
        logger.info(f"总处理节点数: {performance_summary['overall_stats']['total_processed']}")
        logger.info(f"总耗时: {performance_summary['overall_stats']['total_time']:.2f}秒")
        logger.info(f"整体平均速度: {performance_summary['overall_stats']['overall_speed']:.3f}节点/秒")
        logger.info("")
        
        for result in results:
            batch_size = result.get('total_nodes', 0)
            strategy = result.get('strategy_used', result.get('base_strategy', 'unknown'))
            duration = result.get('total_duration', 0)
            speed = result.get('nodes_per_second', 0)
            success_rate = result.get('success_count', 0) / batch_size * 100 if batch_size > 0 else 0
            
            optimization = result.get('optimization_applied', 'none')
            opt_icon = "🔧" if optimization != 'none' else "📊"
            
            logger.info(f"{batch_size:>3}个节点: {opt_icon} {strategy:<5} | {duration:>7.1f}秒 | {speed:.3f}节点/秒 | 成功率: {success_rate:.1f}%")
        
        logger.info(f"{'='*80}")
    
    def close(self):
        """关闭引擎"""
        if hasattr(self, 'adaptive_strategy'):
            self.adaptive_strategy.close()


async def main():
    """主函数"""
    engine = None
    try:
        # 连接Neo4j
        neo4j_config = Config.get_neo4j_config()
        neo4j_conn = Neo4jConnection(
            uri=neo4j_config["uri"],
            user=neo4j_config["user"],
            password=neo4j_config["password"]
        )
        neo4j_conn.verify_connectivity()
        logger.info("成功连接到Neo4j数据库")
        
        # 配置混合处理引擎
        config = ProcessingConfig(
            mode=ProcessingMode.HYBRID_OPTIMAL,
            initial_batch_size=20,
            max_concurrent_batches=3,
            api_rate_limit=8,
            enable_performance_monitoring=True,
            enable_auto_scaling=True,
            fallback_to_sync=True
        )
        
        # 创建混合处理引擎
        engine = HybridProcessingEngine(neo4j_conn, config)
        
        # 设置测试批次大小
        batch_sizes = [10, 50, 100, 150, 200, 250]
        
        # JSON文件路径
        json_file_path = os.path.join(os.path.dirname(__file__), "data", "lhasa_knowledge_graph.json")
        
        # 运行综合实验
        results = await engine.run_comprehensive_experiment(json_file_path, batch_sizes)
        
        logger.info("混合处理引擎综合实验完成！")
        
    except Exception as e:
        logger.error(f"主程序错误: {e}", exc_info=True)
    finally:
        if engine:
            engine.close()


if __name__ == "__main__":
    asyncio.run(main())
